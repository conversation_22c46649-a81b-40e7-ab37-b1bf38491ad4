import os
import xml.etree.ElementTree as ET
from CustomHelper import CGeneralHelper, CResoucedataHelper, CLicenseHelper
import json
import urllib.parse
from typing import List
from CustomLogger import CLogger
import httpx
from EmailNotifier import EmailSender
import configparser
import re

class CRequestValidator:
    ''''
        Class for Pre-Request Validation
    '''
    def __init__(self, bRaiseExceptionOnSingleError: bool = False,bDebug=False):
        self.lsWarningMessages = []
        self.lsInfoMessages = []
        self.lsCriticalErrorMessages = []
        self.bRaiseExceptionOnSingleError = bRaiseExceptionOnSingleError
        self.bAccuVelocityServerRunning = False
        self.bTallyConnected = False
        self.bValidationFileNDir = False
        self.bInternetConnected = False
        self.bOverallValidation = False
        self.bDebug = bDebug
        self.iCountofValidationFilesNErrors = 0
        
    def _load_config(self, xml_path: str) -> ET.Element:
        """
        Loads and returns the <CONFIGVARS> element from the XML file.
        Raises FileNotFoundError or ConfigValidationError on problems.
        """
        if not os.path.isfile(xml_path):
            CLogger.MCWriteLog("error",f"XML config file not found: {xml_path}")
            self.iCountofValidationFilesNErrors +=1
            self.lsCriticalErrorMessages.append(f"XML config file not found: {xml_path}")
            if self.bRaiseExceptionOnSingleError:
                raise FileNotFoundError(f"XML config file not found: {xml_path}")
            return
        try:
            tree = ET.parse(xml_path)
        except ET.ParseError as e:
            CLogger.MCWriteLog("error",f"Failed to parse config XML file '{xml_path}")
            
            self.iCountofValidationFilesNErrors +=1
            self.lsCriticalErrorMessages.append(f"Failed to parse XML file '{xml_path}': {e}")
            if self.bRaiseExceptionOnSingleError:
                raise Exception(f"Failed to parse XML file '{xml_path}': {e}") from e
            return
        
        root = tree.getroot()
        config = root.find('CONFIGVARS')
        if config is None:
            if self.bRaiseExceptionOnSingleError:
                raise Exception("Missing <CONFIGVARS> section in XML.")
            else:
                self.lsCriticalErrorMessages.append("Missing <CONFIGVARS> section in XML.")
                return
        return config



    
    def MValidateConfigFile(self) -> None:
        """
        Validates that required paths exist:
          - AVEXELOC (must be a file)
          - BackUpDest (must be a directory)
          - HelpHTMLPath (must be a file)
          - AVfeatureXLPath (must be a file)
        Raises ConfigValidationError if any are missing/nonexistent.
        """

        config_xml_path = CGeneralHelper.MSGetConfigFilePath() 

        if not config_xml_path:
            CLogger.MCWriteLog("error",f"Config.xml not found at {config_xml_path}")
            self.iCountofValidationFilesNErrors +=1
            self.lsCriticalErrorMessages.append(f"{config_xml_path} File Not Found")
            if self.bRaiseExceptionOnSingleError:
                raise Exception(f"{config_xml_path} File Not Found")
            return

        config = self._load_config(xml_path= config_xml_path)

        required_paths = {
            'AVEXELOC': 'file',
            'BackUpDest': 'dir',
            'TallyComDataPath':'dir',
            'HelpHTMLPath': 'file',
            'AVfeatureXLPath':'file'
        }

        # errors: List[str] = []

        for tag, expected_type in required_paths.items():
            elem = config.find(tag)
            if elem is None or not (elem.text and elem.text.strip()):
                self.lsCriticalErrorMessages.append(f"{tag}: missing or empty in XML.")
                # errors.append(f"{tag}: missing or empty in XML.")
                continue

            path = elem.text.strip()
            if expected_type == 'file':
                if not os.path.isfile(path):
                    if tag == "AVEXELOC":
                        self.iCountofValidationFilesNErrors +=1
                        self.lsWarningMessages.append(
                            f"AccuVelocity Application NOT Connected: Please ensure the AccuVelocity.exe Application location exists in your system: {path}. "
                            f"For example, if this is a network location, connect to it using your credentials and verify the directory exists."
                        )
                    elif tag == "HelpHTMLPath":
                        self.iCountofValidationFilesNErrors +=1
                        self.lsWarningMessages.append(
                            f"AccuVelocity Application Help Page Not Found: Please ensure the file exists in your system: {path}. "
                            f"For example, if this is a network location, connect to it using your credentials and verify the file exists."
                        )

                    elif tag == "AVfeatureXLPath":
                        self.iCountofValidationFilesNErrors +=1
                        self.lsWarningMessages.append(
                            f"AccuVelocity Application Feature List Not Found: Please ensure the file exists in your system: {path}. "
                            f"For example, if this is a network location, connect to it using your credentials and verify the file exists."
                        )
            elif expected_type == 'dir':
                if not os.path.isdir(path):
                    if tag == "BackUpDest":
                        self.iCountofValidationFilesNErrors +=1
                        self.lsWarningMessages.append(
                            f"AccuVelocity Application Backup Destination Not Connected: Please ensure the location exists in your system: {path}. "
                            f"For example, if this is a network location, connect to it using your credentials and verify the directory exists."
                        )
                    elif tag == "TallyComDataPath":
                        self.iCountofValidationFilesNErrors +=1
                        self.lsWarningMessages.append(
                            f"Tally Company Data Location - Not Connected: Please ensure the location exists in your system: {path}. "
                            f"For example, if this is a network location, connect to it using your credentials and verify the directory exists."
                        )
                    else:
                        self.iCountofValidationFilesNErrors +=1
                        self.lsWarningMessages.append("Directory not Found: ", path)
            else:
                self.iCountofValidationFilesNErrors +=1
                self.lsWarningMessages.append(f"{tag}: unknown expected type '{expected_type}' configured.")

        return
    
    
    def MValidateUserConfig(self):
        """
        From User Config Validates that:
        - 'ExportRequest.ReqUrl' exists
        - Its IP matches the current local IP
        - Its port is open and listening
        - FilteredGetItemsReqFilePath exusts
        - AbhinavDayBookPath
        """
        bCreateNewTallyConnectURl = False
        server_port = None
        # Getting code from Tally.ini
        tally_ini_path = None
        # Reading UserConfig.json
        userConfigData = CResoucedataHelper.MSGetUserConfig()

        # Getting Path for User Config
        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        strUserConfigFileName = CGeneralHelper.MSGetUserConfigFileName()
        strUserConfigFilePath = os.path.join(strResourceDirpath, strUserConfigFileName)



        # Step 1: IP and Port Validation

        # Extract URL
        try:
            req_url = userConfigData["ExportRequest"]["ReqUrl"]
        except KeyError:
            CLogger.MCWriteLog("error",f"Missing 'ExportRequest.ReqUrl' in UserConfig.")
            self.lsCriticalErrorMessages.append(f"Missing 'ExportRequest.ReqUrl' in UserConfig.")
            if self.bRaiseExceptionOnSingleError:
                raise Exception("Missing 'ExportRequest.ReqUrl' in UserConfig.")
            # raise Exception("Missing 'ExportRequest.ReqUrl' in JSON.")

        parsed = urllib.parse.urlparse(req_url)
        if not parsed.scheme.startswith("http"):
            # self.lsInfoMessages.append(f"Invalid scheme in URL: {req_url} in UserConfig")
            bCreateNewTallyConnectURl = True
            if self.bRaiseExceptionOnSingleError:
                raise Exception(f"Invalid scheme in URL: {req_url} in UserConfig")
        
        # if want to read from config file
        # req_ip = parsed.hostname

        req_ip = userConfigData["ExportRequest"]["ReqIP"]
        if bCreateNewTallyConnectURl:
            req_port = 9000
        else:
            req_port = parsed.port

        try:
            tally_ini_path = userConfigData["TALLY_INI"]
            if not os.path.exists(tally_ini_path):
                raise FileNotFoundError("Tally.ini not found at {tally_ini_path} mentioned in UserConfig")

        except KeyError:
            self.iCountofValidationFilesNErrors+=1
            CLogger.MCWriteLog("error",f"Missing 'TALLY_INI' in UserConfig.")
            self.lsCriticalErrorMessages.append(f"Missing 'TALLY_INI' in UserConfig.")
            if self.bRaiseExceptionOnSingleError:
                raise Exception("Missing 'TALLY_INI' in UserConfig.")
        
        except FileNotFoundError:
            self.iCountofValidationFilesNErrors+=1
            CLogger.MCWriteLog("error",f"Tally.ini not found at {tally_ini_path} mentioned in UserConfig")
            self.lsCriticalErrorMessages.append(f"Tally.ini not found at {tally_ini_path} mentioned in UserConfig")
            if self.bRaiseExceptionOnSingleError:
                raise FileNotFoundError(f"Tally.ini not found at {tally_ini_path} mentioned in UserConfig")

        # Reading Tally.ini file
        try:
            config = configparser.ConfigParser(
                strict=False,               # allow duplicate options; last one wins
                allow_no_value=True,        # tolerate keys without '=' just in case
                interpolation=None          # avoid % interpolation surprises
                )
            if tally_ini_path:
                config.read(tally_ini_path)
            server_port = config.getint('TALLY', 'ServerPort')
        except Exception as e:
            self.iCountofValidationFilesNErrors+=1
            CLogger.MCWriteLog("error",f"Not Able read Tally.ini {str(e)}")
            self.lsCriticalErrorMessages.append(f"Not Able read Tally.ini {str(e)}")
            if self.bRaiseExceptionOnSingleError:
                raise Exception("Not Able read Tally.ini")
            return
        

        # if not req_ip or not req_port:
        #     CLogger.MCWriteLog("error",f"Invalid or missing IP/port in ReqUrl: {req_url} in UserConfig")
        #     raise Exception(f"Invalid or missing IP/port in ReqUrl: {req_url} in UserConfig")

        if server_port != req_port:
            match = re.match(r"(http://[\d\.]+:)(\d+)(/?)", req_url)
            if match:
                req_port = int(match.group(2))  # Current port in JSON

                # Reconstruct the URL with the correct port
                new_url = f"http://{req_ip}:{server_port}{match.group(3)}"
                userConfigData["ExportRequest"]["ReqUrl"] = new_url

                # Step 4: Write back the updated JSON
                with open(strUserConfigFilePath, "w") as f:
                    json.dump(userConfigData, f, indent=4)

                # Updating Request Port
                req_port = server_port
                CLogger.MCWriteLog("info","Port Updated with Accordance to tally.ini file")

        # If want to check IP from UserConfig instead of localhost
        # local_ip = CGeneralHelper.MSGetLocalIPAddress()
        # if req_ip != local_ip:
        #     CLogger.MCWriteLog("error",f"ReqUrl IP ({req_ip}) does not match local IP ({local_ip}) in UserConfig")
        #     raise Exception(f"ReqUrl IP ({req_ip}) does not match local IP ({local_ip}) in UserConfig")
        self.bTallyConnected = CGeneralHelper.MSIsPortOpen(req_ip, req_port)
        if not self.bTallyConnected:
            CLogger.MCWriteLog("error",f"Port {req_port} on IP {req_ip} is not open or not responding in UserConfig")
            self.lsWarningMessages.append(f"Tally Not Connected: Port {req_port} on IP {req_ip} is not open. "
                f"Please restart the Tally application or change the port manually in Tally: "
                f"Tally Gateway Page → Help → Settings → Connectivity → Client/Server Configuration. "
                f"Ensure 'TallyPrime acts as' is set to 'Both', 'Enable ODBC' is set to 'Yes', and 'Port' is set to '9000'."
            )
            if self.bRaiseExceptionOnSingleError:
                raise Exception(f"Port {req_port} on IP {req_ip} is not open or not responding in UserConfig")
        

        # Step 2 : Validate Path in UserConfig

        strResourceFilePath = CGeneralHelper.MSGetResourceDirectory()


        # Validating: strFilteredGetItemsReqFilePath
        try:
            strFilteredGetItemsReqFilePath = userConfigData["ExportRequest"]["FilteredGetItemsReqFilePath"]
        except KeyError:
            self.iCountofValidationFilesNErrors +=1
            CLogger.MCWriteLog("error",f"Missing 'ExportRequest.FilteredGetItemsReqFilePath' in UserConfig.")
            self.lsCriticalErrorMessages.append(f"Missing 'ExportRequest.FilteredGetItemsReqFilePath' in UserConfig.")
            if self.bRaiseExceptionOnSingleError: 
                raise Exception("Missing 'ExportRequest.FilteredGetItemsReqFilePath' in JSON.")
        
        strFilteredGetItemsReqFilePath = os.path.join(strResourceFilePath, strFilteredGetItemsReqFilePath)

        if not os.path.exists(strFilteredGetItemsReqFilePath):
            self.iCountofValidationFilesNErrors +=1
            self.lsCriticalErrorMessages.append(f"In Userconfig FilteredGetItemsReqFilePath File Path {strFilteredGetItemsReqFilePath} Does not Exist.")
            if self.bRaiseExceptionOnSingleError: 
                self.lsCriticalErrorMessages.append(f"In Userconfig FilteredGetItemsReqFilePath File Path {strFilteredGetItemsReqFilePath} Does not Exist.")
                raise Exception(f"In Userconfig FilteredGetItemsReqFilePath File Path {strFilteredGetItemsReqFilePath} Does not Exist.")
        
        # Validating: strFilteredGetItemsReqFilePath

        try:
            strAbhinavDayBookPath = userConfigData["ExportRequest"]["AbhinavDayBookPath"]
        except KeyError:
            self.iCountofValidationFilesNErrors +=1
            CLogger.MCWriteLog("error",f"Missing 'ExportRequest.AbhinavDayBookPath' in UserConfig.")
            self.lsCriticalErrorMessages.append(f"Missing 'ExportRequest.AbhinavDayBookPath' in UserConfig.")
            if self.bRaiseExceptionOnSingleError: 
                raise Exception("Missing 'ExportRequest.AbhinavDayBookPath' in JSON.")
        strAbhinavDayBookPath = os.path.join(strResourceFilePath, strAbhinavDayBookPath)
        if not os.path.exists(strAbhinavDayBookPath):
            self.iCountofValidationFilesNErrors +=1
            CLogger.MCWriteLog("error",f"In Userconfig AbhinavDayBookPath File Path {strAbhinavDayBookPath} Does not Exist.")
            self.lsCriticalErrorMessages.append(f"In Userconfig AbhinavDayBookPath File Path {strAbhinavDayBookPath} Does not Exist.")
            
            if self.bRaiseExceptionOnSingleError: 
                raise Exception(f"In Userconfig AbhinavDayBookPath File Path {strAbhinavDayBookPath} Does not Exist.")

        return 
    
    
    def MValidateMasterAPI(self):
        """
            Calls the master API health check URL and returns whether it's healthy.

            Args:
                url (str): The full URL to the health check endpoint.
                timeout (float): Timeout for the HTTP request in seconds.

            Returns:
                bool: True if the API returns 200 OK, False otherwise.

            Raises:
                httpx.RequestError: For network-related issues.
                httpx.HTTPStatusError: For unexpected HTTP status codes.
    """
        self.bAccuVelocityServerRunning = False
        strMasterAPIHealthCheck = ""
        # Getting Developer Config
        developer_config_data = CResoucedataHelper.MSGetDeveloperConfig()

        # Getting System Name
        strSystemName = os.getlogin()

        # Getting License File
        dictLicense = CLicenseHelper.MSVerifyLicense()

        # Client Name
        strClientName = dictLicense['name']
        bDeveloperMode = dictLicense['DEVELOPER_MODE']
        
        # Extracting API from Developer Config
        try:
            strMasterAPIHealthCheck = developer_config_data["ExportRequest"]["APIEndPoints"]["MasterAPIHealthCheck"]
            strMailFrom = developer_config_data["EmailConfig"]["strMailFrom"]
            strPassword = developer_config_data["EmailConfig"]["strPassword"]
            strServer = developer_config_data["EmailConfig"]["strServer"]
            intPort = developer_config_data["EmailConfig"]["intPort"]
            current_ip = CGeneralHelper.MSGetLocalIPAddress()
            if current_ip != "************":
                lsToAddress = developer_config_data["EmailConfig"]["lsMailTo"]
                lsCCAddress = developer_config_data["EmailConfig"]["lsMailToCC"]
                if bDeveloperMode and "<EMAIL>" in lsCCAddress:
                    lsCCAddress.remove("<EMAIL>")
                # Exclude Anirudh Sir When Developer Mode ON
                # TODO: Client Name Add in Subject
            else: 
                lsToAddress = ["<EMAIL>"]
                lsCCAddress = []
            
        except Exception as e:
            CLogger.MCWriteLog("error", f"Missing required configuration key: {e} in DeveloperConfig")
            self.lsCriticalErrorMessages.append(f"Missing required configuration key: {e} in DeveloperConfig")
            if self.bRaiseExceptionOnSingleError:
                raise Exception(f"Missing required configuration key: {e} in DeveloperConfig")

        if not strMasterAPIHealthCheck:
            CLogger.MCWriteLog("error",f"MasterAPIHealthCheck not found in DeveloperConfig.")
            self.lsCriticalErrorMessages.append(f"MasterAPIHealthCheck not found in DeveloperConfig.")
            if self.bRaiseExceptionOnSingleError:
                raise Exception("MasterAPIHealthCheck not found in DeveloperConfig.")

        # Calling Health CheckAPI
        try:
            response = httpx.get(strMasterAPIHealthCheck, timeout=10)
            response.raise_for_status() 
            if response.status_code == 200:
                self.bAccuVelocityServerRunning = True 
            elif response.status_code == 207:
                dictAPIStatus = json.loads(response.text)
                raise Exception(f"{dictAPIStatus['API2']['message']}")
                
                # if self.bRaiseExceptionOnSingleError:

            CLogger.MCWriteLog("INFO","Both API services are running")
           
        except Exception as e:

            if "timed out" in str(e):
                developer_message = (
                    "🚨 Connection failure detected while attempting to reach the Master API.\n"
                    f"The FastAPI(Main) {strMasterAPIHealthCheck} API is down, unreachable, or experiencing issues.\n"
                    "Immediate investigation is recommended. Logs and error details are available in the system.\n"
                    "This message was triggered automatically for awareness and further action."
                )
            else:
                developer_message = (
                    "🚨 Connection failure detected while attempting to reach the Master API.\n"
                    f"{str(e)}.\n"
                    "Immediate investigation is recommended. Logs and error details are available in the system.\n"
                    "This message was triggered automatically for awareness and further action."
                )

            user_message = (
                "⚠️ Unable to connect to the Accuvelocity Server. The server may be temporarily down or unreachable. "
                "Our technical team has been notified and is working to resolve the issue. "
                "We appreciate your patience."
            )
            self.lsWarningMessages.append(user_message)
            html_content = EmailSender.generate_api_error_html(
                error_type="RequestError",
                error_detail=developer_message,
                system_name = strSystemName,
                client_name = strClientName
            )
            CLogger.MCWriteLog("error",developer_message)
            EmailSender.send_email(from_address = strMailFrom,password= strPassword,to_addresses=lsToAddress,subject = f"{strClientName} Server/Service Not Reachable", smtp_server = strServer, smtp_port = intPort, html_content=html_content,cc_addresses=lsCCAddress)
            if self.bRaiseExceptionOnSingleError:
                raise Exception(user_message)
            return

    
    def MSRunAllValidation(self) -> None:
        """
        Master entry point: runs all validation routines. Extend here with new validators.
        Raises ConfigValidationError if any sub-validation fails.
        """
        try:
            CLogger.MCSetupLogging()
            self.MValidateConfigFile()
            if self.bDebug:
                print("-------------------- 1 -----------------")
                print("Ls Warning Messages:", self.lsWarningMessages)
                print("Ls Critical Error Messages:", self.lsCriticalErrorMessages)
                print("Ls Info Messages:", self.lsInfoMessages)
            self.MValidateUserConfig()
            if self.bDebug:
                print("-------------------- 2 -----------------")
                print("Ls Warning Messages:", self.lsWarningMessages)
                print("Ls Critical Error Messages:", self.lsCriticalErrorMessages)
                print("Ls Info Messages:", self.lsInfoMessages)
            self.MValidateMasterAPI()
            if self.bDebug:
                print("-------------------- 3 -----------------")
                print("Ls Warning Messages:", self.lsWarningMessages)
                print("Ls Critical Error Messages:", self.lsCriticalErrorMessages)
                print("Ls Info Messages:", self.lsInfoMessages)
        except Exception as e:
            raise e
    
    def check_internet_connection():
        """Simple internet connectivity check"""
        try:
            import httpx
            response = httpx.get("https://www.google.com", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def MValidateFilesNDirError(self) -> bool:
        """
        Check if processing is allowed based on validation results
        Returns True if no File & Directories errors or warnings, False otherwise
        """
        # 0 Validation Files & Directory Warning
        self.bValidationFileNDir = self.iCountofValidationFilesNErrors == 0
        return self.bValidationFileNDir
    
    def MAllowToProcess(self) -> bool:
        """
        Check if processing is allowed based on validation results
        Returns True if no critical errors or warnings, False otherwise
        """
        self.bInternetConnected = CRequestValidator.check_internet_connection()
        self.bOverallValidation = len(self.lsCriticalErrorMessages) == 0 and len(self.lsWarningMessages) == 0 and self.bInternetConnected # Internet Should ON 
        return self.bOverallValidation


if __name__ == "__main__":
    objRequestValidator = CRequestValidator()
    objRequestValidator.MSRunAllValidation()