import os
import pandas as pd
from datetime import datetime
import traceback
from CustomLogger import CLogger

class CActivityReportMerger:
    def __init__(self, main_directory, output_directory="ActivityLog"):
        # main_directory is now the relative path of the parent directory where the datetimestamp directories are
        self.main_directory = main_directory
        self.output_directory = output_directory
        self.generated_files = []  # To store generated file paths

    def merge_and_split_files(self):
        # Ensure the output directory exists
        if not os.path.exists(self.output_directory):
            os.makedirs(self.output_directory)
            CLogger.MCWriteLog("info", f"Output directory {self.output_directory} created.")
        
        # Keywords to look for in filenames
        keywords = ["PV-WITH-INVENTORY", "PV-WITHOUT-INVENTORY", "DELIVERY-NOTE", "BANK-STATEMENT","JOURNAL","Other"]
        version_data = {keyword: {} for keyword in keywords}

        # Walk through all the directories in the main directory
        for root, dirs, _ in os.walk(self.main_directory):
            # Only look for report files inside subdirectories of the parent directory
            for dir in dirs:
                for _, _, files in os.walk(os.path.join(root, dir)):
                    for file in files:
                        if (file.startswith('Report_') and file.endswith('.csv')) or (file.startswith('Report_') and file.endswith('.xlsx')):
                            # Extract keyword from the filename
                            keyword = next((kw for kw in keywords if kw in file), "Other")
                            CLogger.MCWriteLog("debug", f"Processing file: {file} with keyword: {keyword}")

                            # Try to extract version info from the filename (e.g., v1, v2, etc.)
                            version_parts = file.split('_')[-1]
                            version = "V1" if "V1" in version_parts else 'Other'

                            if version not in version_data[keyword]:
                                version_data[keyword][version] = []

                            file_path = os.path.join(root, dir, file)
                            try:
                                # Read the CSV file into a DataFrame
                                df = pd.read_csv(file_path)
                                version_data[keyword][version].append(df)  # Store DataFrame by version
                                CLogger.MCWriteLog("info", f"Successfully read file: {file_path}")
                            except Exception as e:
                                CLogger.MCWriteLog("error", f"Error reading {file_path}: {e}")
                                CLogger.MCWriteLog("debug", f"Traceback: {traceback.format_exc()}")
                                continue

        # Generate current timestamp for output filename
        current_timestamp = datetime.now().strftime('%Y%m%d_%H%M')

        # Merge files by keyword and version
        for keyword, versions in version_data.items():
            for version, dfs in versions.items():
                if dfs:  # Proceed if there are files for the version
                    # Merge all DataFrames for the current version
                    merged_data = pd.concat(dfs, ignore_index=True)

                    # Sort by "Received Date" if it exists
                    if 'Received Date' in merged_data.columns:
                        merged_data['Received Date'] = pd.to_datetime(merged_data['Received Date'], errors='coerce', dayfirst=True)
                        merged_data = merged_data.sort_values(by='Received Date', ascending=False)

                    if 'Start Execution Time' in merged_data.columns:
                        merged_data['Start Execution Time'] = pd.to_datetime(merged_data['Start Execution Time'], errors='coerce', dayfirst=True)
                        merged_data = merged_data.sort_values(by='Start Execution Time', ascending=False)
                    
                    # Format merged data
                    formatted_data = self.format_merged_data(merged_data)

                    # Create output filename
                    output_filename = f'ActivityLog_{current_timestamp}_{keyword}_{version}.xlsx'
                    output_path = os.path.join(self.output_directory, output_filename)

                    # Save formatted data to Excel
                    formatted_data.to_excel(output_path, index=False)

                    CLogger.MCWriteLog("info", f'Merged and formatted Excel file saved as: {output_path}')

                    # Store the full file path in the list of generated files
                    self.generated_files.append(output_path)

        # Return the list of generated Excel file paths
        return self.generated_files

    def format_merged_data(self, data):
        # Customize the formatting of your data here
        # For example, renaming columns, adding headers, formatting numbers, etc.

        # Example: Renaming columns
        column_mappings = {
            'Received Date': 'Date Received',
            'Other Column': 'New Column Name'  # Add more mappings as needed
        }
        data = data.rename(columns=column_mappings)

        # Example: Formatting date columns
        if 'Date Received' in data.columns:
            data['Date Received'] = data['Date Received'].dt.strftime('%d-%m-%Y')

        # Example: Formatting numbers (e.g., two decimal places)
        if 'Amount' in data.columns:
            data['Amount'] = data['Amount'].apply(lambda x: f'{x:.2f}')

        return data

if __name__ == "__main__":
    CLogger.MCSetupLogging()
    report_merger = CActivityReportMerger(main_directory=r"C:\Users\<USER>\Desktop\customer\REAL\Accuvelocity_exe\Separtefolder")
    generated_files = report_merger.merge_and_split_files()
    print(generated_files)  # This will print the list of saved Excel file paths
