@echo off
setlocal

REM Set virtual environment path (change if needed)
set VENV_DIR=envAccuvelocity
set VENV_ACTIVATE=%VENV_DIR%\Scripts\activate.bat

REM Check if virtualenv exists
if not exist "%VENV_ACTIVATE%" (
    echo Creating virtual environment...
    python -m venv %VENV_DIR%
)

REM Activate virtualenv
echo Activating virtual environment...
call "%VENV_ACTIVATE%"

REM Install required packages
@REM echo Installing required packages...
@REM pip install --upgrade pip
@REM pip install pyinstaller cryptography

REM Build the executable
echo Building single-file executable...
pyinstaller --onefile Sourcecode\ConfigMerger.py --name MergeConfig

REM Check if build was successful
if exist dist\MergeConfig.exe (
    echo.
    echo ✅ EXE created successfully at dist\MergeConfig.exe
) else (
    echo.
    echo ❌ Failed to create EXE.
)

pause
