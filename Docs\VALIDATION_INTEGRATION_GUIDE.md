# Validation Integration Guide

## Overview

The AccuVelocity application now includes automatic pre-request validation that runs before file processing. This ensures that all system requirements are met before attempting to process documents.

## How It Works

### 1. Validation Flow

```
Main.py Execution
       ↓
Pre-Request Validation
       ↓
   Check Results
       ↓
┌─────────────────┐    ┌─────────────────┐
│ Issues Found?   │    │ No Issues?      │
│ (bAllowProcessing│    │ (bAllowProcessing│
│ = False)        │    │ = True)         │
└─────────────────┘    └─────────────────┘
       ↓                       ↓
Show Validation UI         Proceed with
& Exit                     File Processing
```

### 2. Validation Logic

**In Main.py:**
```python
# Run validation
objValidator = CRequestValidator(bRaiseExceptionOnSingleError=False)
objValidator.MSRunAllValidation()

# Check if processing is allowed
bAllowProcessing = objValidator.MAllowToProcess()

if not bAllowProcessing:
    # Show validation UI and exit
    validation_window = launch_system_check()
    app.exec_()
    sys.exit(0)
else:
    # Proceed with file processing
    objFileProcessor = CFileProcessor(...)
```

### 3. Validation Criteria

**Processing is BLOCKED when:**
- Critical errors exist (file paths missing, configuration issues)
- Warning messages exist (Tally not connected, server issues)

**Processing is ALLOWED when:**
- No critical errors
- No warning messages
- Only informational messages (if any)

## Validation Components

### 1. CRequestValidator
- **Location**: `PreRequestValidation.py`
- **Purpose**: Performs system validation checks
- **Key Methods**:
  - `MSRunAllValidation()`: Runs all validation checks
  - `MAllowToProcess()`: Returns True/False based on validation results

### 2. SimpleValidationUI
- **Location**: `SimpleValidationUI.py`
- **Purpose**: Displays validation results to user
- **Features**:
  - Connection status indicators
  - Issue details with SR NO prefixes
  - Trigger-based background colors (red/green)
  - Total issue count in title

### 3. launch_system_check Function
- **Location**: `SimpleValidationUI.py`
- **Purpose**: Launches the validation UI
- **Function**: `launch_system_check()` - Returns window instance for external control

## Validation Checks Performed

### 1. Connection Checks
- **Tally Connection**: Checks if Tally server is accessible
- **AccuVelocity Server**: Verifies API server connectivity
- **Internet Connection**: Tests external connectivity

### 2. File & Directory Validation
- **Configuration Files**: Ensures required config files exist
- **Request Templates**: Validates XML request templates
- **Output Directories**: Checks if output paths are accessible

### 3. System Requirements
- **License Validation**: Verifies valid license
- **Dependencies**: Checks required modules and libraries

## User Experience

### When Issues Are Found
1. **Automatic Detection**: System automatically detects issues
2. **UI Display**: Validation UI opens showing:
   - Connection status (red/green indicators)
   - Detailed issue list with SR NO prefixes
   - Total issue count in title
   - Red background indicating problems
3. **User Action Required**: User must resolve issues before processing
4. **No File Processing**: Application exits without processing files

### When No Issues Found
1. **Silent Operation**: No UI shown to user
2. **Direct Processing**: Application proceeds directly to file processing
3. **Background Logging**: Validation results logged for debugging

## Configuration

### Enable/Disable Validation
The validation is automatically enabled in Main.py. To modify behavior:

```python
# In Main.py, comment out validation section to disable
# ------------------- Pre-Request Validation -------------------
# ... validation code here ...
```

### Validation Sensitivity
Modify validation criteria in `PreRequestValidation.py`:

```python
def MAllowToProcess(self) -> bool:
    # Current: Block on any critical errors or warnings
    self.bOverallValidation = len(self.lsCriticalErrorMessages) == 0 and len(self.lsWarningMessages) == 0
    
    # Alternative: Block only on critical errors
    # self.bOverallValidation = len(self.lsCriticalErrorMessages) == 0
    
    return self.bOverallValidation
```

## Logging

All validation activities are logged:

```
INFO: Starting pre-request validation...
INFO: Validation completed. Allow processing: False
INFO: Validation summary - Critical: 2, Warnings: 1, Info: 0
WARNING: Validation issues found (3 issues). Showing validation UI...
ERROR: CRITICAL: File path not found
WARNING: WARNING: Tally not connected
INFO: Launching validation UI...
```

## Troubleshooting

### Common Issues

1. **Validation UI Not Showing**
   - Check if `SimpleValidationUI.py` exists and has `launch_system_check` function
   - Verify PyQt5 is properly installed
   - Check logs for UI launch errors

2. **False Positives**
   - Review validation criteria in `MAllowToProcess()`
   - Check if validation checks are too strict
   - Verify configuration files are properly formatted

3. **Performance Issues**
   - Validation adds ~2-3 seconds to startup
   - Consider caching validation results for repeated runs
   - Optimize network connectivity checks

### Debug Mode

Enable debug logging to see detailed validation steps:

```python
objValidator = CRequestValidator(bRaiseExceptionOnSingleError=False, bDebug=True)
```

## Benefits

1. **Prevents Processing Failures**: Catches issues before file processing starts
2. **User-Friendly**: Clear visual feedback about system status
3. **Comprehensive**: Checks all critical system components
4. **Automatic**: No user intervention required when system is healthy
5. **Informative**: Detailed error messages help users resolve issues quickly
