SR. NO,Voucher Type,Vendor / Bank / Document Name,AccuVelocity Validation,AccuVelocity PriceList Verification,AccuVelocity NOTE
1,RECEIPT_NOTE,FEE RECEIPT,"1. Use a consistent format for Tally ledger names: Name_OnlineRegNo (e.g., ATHARV_8569).
2. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,It is preferable to send the provided generated fee receipt as a digital document.
2,JOURNAL,Inifinity Cars,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"It�s better to send digital (PDF or soft copy) documents whenever possible.
If you need to send scanned documents, please make sure they are clear and easy to read by:
Scanning at a high resolution (more than 200 DPI)
Making sure the pages are straight and properly aligned (not tilted or skewed)"
3,JOURNAL,R<PERSON>ra <PERSON>ak Security & Management,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"It�s better to send digital (PDF or soft copy) documents whenever possible.
If you need to send scanned documents, please make sure they are clear and easy to read by:
Scanning at a high resolution (more than 200 DPI)
Making sure the pages are straight and properly aligned (not tilted or skewed)"
4,JOURNAL,Sanghi Brothers Indore Pvt. LTD.,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"It�s better to send digital (PDF or soft copy) documents whenever possible.
If you need to send scanned documents, please make sure they are clear and easy to read by:
Scanning at a high resolution (more than 200 DPI)
Making sure the pages are straight and properly aligned (not tilted or skewed)"
5,JOURNAL,R K Enterprises,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"It�s better to send digital (PDF or soft copy) documents whenever possible.
If you need to send scanned documents, please make sure they are clear and easy to read by:
Scanning at a high resolution (more than 200 DPI)
Making sure the pages are straight and properly aligned (not tilted or skewed)"
6,JOURNAL,Trophy Wala,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"It�s better to send digital (PDF or soft copy) documents whenever possible.
If you need to send scanned documents, please make sure they are clear and easy to read by:
Scanning at a high resolution (more than 200 DPI)
Making sure the pages are straight and properly aligned (not tilted or skewed)"
7,JOURNAL,Ghansyam Bahiniya,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"It�s better to send digital (PDF or soft copy) documents whenever possible.
If you need to send scanned documents, please make sure they are clear and easy to read by:
Scanning at a high resolution (more than 200 DPI)
Making sure the pages are straight and properly aligned (not tilted or skewed)"
8,JOURNAL,Mendwell AGENCIES,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"It�s better to send digital (PDF or soft copy) documents whenever possible.
If you need to send scanned documents, please make sure they are clear and easy to read by:
Scanning at a high resolution (more than 200 DPI)
Making sure the pages are straight and properly aligned (not tilted or skewed)"
9,BANK_STATEMENT,Bank Statement, "HDFC_2732 (**************) and HDFC_3648 (**************)",NO,"Please upload the bank statements in either XLSX, CSV, or XLS format."