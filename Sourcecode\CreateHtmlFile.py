import pandas as pd
import os

def csv_to_html(input_csv: str, output_html_dir: str):
    """
    Converts a CSV file to a formatted HTML table with styling.
    Replaces NaN values with blanks and ensures new lines in text columns are converted to <br> tags.
    
    Parameters:
        input_csv (str): Path to the input CSV file.
        output_html_dir (str): Path to save the formatted HTML file.
    """
    try:
        # Load CSV file with a fallback encoding
        df = pd.read_csv(input_csv, dtype=str, encoding='utf-8')  # Try reading with UTF-8

        # If that fails, try with ISO-8859-1 encoding
    except UnicodeDecodeError:
        df = pd.read_csv(input_csv, dtype=str, encoding='ISO-8859-1')
    
    # Convert NaN values to empty strings
    df = df.fillna("")

    # Replace \n with <br> in all string columns
    df = df.applymap(lambda x: x.replace("\r", "") if isinstance(x, str) else x)
    # Replace \n with <br> in all string columns
    df = df.applymap(lambda x: x.replace("\n", "<br>") if isinstance(x, str) else x)
    # Convert DataFrame to HTML with styling
    html_output = df.to_html(index=False, classes="table table-striped", escape=False)

    # Add basic styling
    styled_html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
                padding: 20px;
                background-color: #f4f4f4;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                background-color: white;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }}
            th {{
                background-color: #4CAF50;
                color: white;
            }}
            tr:nth-child(even) {{
                background-color: #f2f2f2;
            }}
        </style>
    </head>
    <body>
        {html_output}
    </body>
    </html>
    """

    # Extract the filename without extension from the input CSV
    filename = os.path.basename(input_csv)  # Get the file name with extension
    filename_without_extension = os.path.splitext(filename)[0]  # Remove the extension

    # Combine output directory and new filename with .html extension
    output_html_path = os.path.join(output_html_dir, filename_without_extension + ".html")

    # Save to file
    with open(output_html_path, "w", encoding="utf-8") as f:
        f.write(styled_html)
    
    print(f"Formatted HTML file saved at: {output_html_path}")

def process_csv_files_in_directory(input_dir, output_html_dir):
    # Ensure the output directory exists
    if not os.path.exists(output_html_dir):
        os.makedirs(output_html_dir)
        print(f"Created output directory: {output_html_dir}")
    
    # Iterate through all files in the directory
    for filename in os.listdir(input_dir):
        # Check if the file is a CSV
        if filename.endswith(".csv"):
            input_csv = os.path.join(input_dir, filename)
            # Run the csv_to_html function for each CSV file
            csv_to_html(input_csv=input_csv, output_html_dir=output_html_dir)

if __name__ == "__main__":
    # Directory paths
    input_dir = r"Data\AV_Feature_Details"  # Path to the directory containing CSV files
    output_html_dir = r"Data\AV_Feature_Details"  # Directory to save the HTML files
    # Call the method to process the CSV files
    process_csv_files_in_directory(input_dir, output_html_dir)
