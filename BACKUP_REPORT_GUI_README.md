# AccuVelocity Backup Report GUI

## Overview
The Backup Report GUI provides a comprehensive interface for viewing and managing backup file reports in AccuVelocity. It displays backup information with professional formatting and export capabilities.

## Features

### 📊 **Single Table Interface**
- **Unified View**: All backups displayed in one sortable table
- **Date-based Sorting**: Click column headers to sort by date, size, or type

### 🎨 **Visual Enhancements**
- **Professional Table Layout**: Sortable columns with proper spacing
- **Alternating Row Colors**: Enhanced readability
- **Total Size Display**: Shows total backup size in window title

### 📋 **Data Display**
Each backup entry shows:
- **Sr No**: Sequential number for easy reference
- **Backup Date**: Specific date of backup (DD-MMM-YY format)
- **Backup Name**: Generated backup filename
- **Backup Type**: Categorized as Daily, Weekly, or Monthly
- **Size**: Individual backup size in MB/GB format
- **Actions**: View and Delete button to open backup location

### 🔄 **Functionality**
- **Refresh Data**: Updates backup information and recalculates sizes
- **Export to TXT**: Generates comprehensive text reports with system info
- **View Location**: Open backup directory or specific backup folder
- **Empty Directory Handling**: User-friendly message when no backups found
- **Professional Formatting**: Clean, organized display

## Usage

### Method 1: Command Line Launch
```bash
# Launch the backup report GUI directly
python Main.py --show-backup-report-gui
```

## GUI Components

### Main Window
- **Title**: "AccuVelocity - Backup Report Viewer (Total: X.XX GB)"
- **Size**: 1400x700 pixels (resizable)
- **Icon**: AccuVelocity logo
- **Layout**: Single table interface with control buttons

### Table
- **Columns**: Sr No (80px), Date (150px), Backup Name (300px), Type (120px), Size (100px), Actions (120px)
- **Features**: Row selection, alternating colors, sortable headers, color-coded rows
- **Data**: Automatically populated from JSON reports with real-time size calculation
- **Empty State**: User-friendly message when no backups are found

### Buttons
- **Refresh Data**: Regenerates backup reports, recalculates sizes, and updates display
- **Export to TXT**: Creates formatted text report with system information and timestamps
- **View Backup Location**: Opens main backup directory in file explorer
- **View (per row)**: Opens specific backup folder in file explorer
- **Close**: Closes the application

## Export Format

The TXT export includes:
- **Header**: Report title, generation timestamp, system information, and request ID
- **System Info**: Device name, system name, total backup size, backup directory
- **All Backups Section**: Complete list of all backups with sizes
- **Summary**: Total counts by category and storage usage
- **Empty State Handling**: Clear message when no backups are found

### Sample Export Structure
```
====================================================================================================
ACCUVELOCITY BACKUP REPORT
====================================================================================================
Generated on: 2025-06-25 14:30:45
Device Name: DESKTOP-ABC123
System Name: Windows
Request ID: REQ_A1B2C3D4
Total Backup Size: 2.45 GB
Backup Directory: H:\Temp

ALL BACKUP FILES
----------------------------------------------------------------------------------------------------
Sr No    Date           Backup Name                              Type       Size
----------------------------------------------------------------------------------------------------
1        <USER>      <GROUP>                   Daily      245.67 MB
2        18-Jun-25      Backup_18-Jun-25_2043                   Weekly     512.34 MB
3        12-Jun-25      Backup_12-Jun-25_1643                   Monthly    1.23 GB

SUMMARY
--------------------------------------------------
Total Weekly Backups: 2
Total Monthly Backups: 1
Grand Total: 8
Total Storage Used: 2.45 GB
```

### Empty Directory Export
```
====================================================================================================
ACCUVELOCITY BACKUP REPORT
====================================================================================================
Generated on: 2025-06-25 14:30:45
Device Name: DESKTOP-ABC123
System Name: Windows
Request ID: REQ_A1B2C3D4
Total Backup Size: 0 B
Backup Directory: H:\Temp

NO BACKUP FILES FOUND
--------------------------------------------------
No backup files were found in the backup directory.
Please ensure that backups are being created properly.
```

## Error Handling

The GUI includes comprehensive error handling for:
- **Missing Files**: Graceful handling when backup files don't exist
- **JSON Parsing**: Safe loading of potentially corrupted data files
- **GUI Errors**: User-friendly error messages with logging
- **Export Failures**: File permission and path validation

## Integration

The Backup Report GUI integrates seamlessly with:
- **Main Application**: Via command line arguments
- **Backup Manager**: Automatic data synchronization
- **Logging System**: Comprehensive error tracking
- **Resource Management**: Proper file and icon handling

## Future Enhancements

Potential improvements could include:
- **Filtering Options**: Date range and type filters
- **Search Functionality**: Find specific backups
- **Backup Restoration**: Direct restore from GUI
- **Scheduling**: Automated report generation
- **Email Reports**: Automated report distribution
