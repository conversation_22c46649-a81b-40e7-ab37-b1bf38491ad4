;OM ;<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>

;Author - <PERSON><PERSON><PERSON> | Owner - AccuVelocity | Version - 2 Beta | Date - 22nd January 2025

;Clear Pipeline Button used to clear data from Tally API server
[Button:ClearPipeLine]
	Key:Ctrl+F
	Title:Finish Import
	Action:Browse Url Ex:"H:\AI Data\ClearData.bat" ;Replace With AVPipelineClear bat file path
	
;Adding Clear Pipeline Button to Tally
[#Menu:Gateway of Tally]
	Buttons:ClearPipeLine
	
;Adding SubMenu of AccuVelocity to Tally
[#Menu : Gateway of Tally]
	Add:Key Item:After:@@locIndentUtilities: AccuVelocityAI:V:Menu:AccuVelocity AI
	Add:Indent:After:@@locIndentUtilities:AccuVelocityAI
	
;Adding Items to SubMenu of AccuVelocity to Tally
[Menu: AccuVelocityAI] 
	Add:Indent:After:Indent:Process
	Add:Item:AccuVelocity Voucher Processing:Create:ExeLoc
	Add:Button:ClearPipeLine ;Adding Clear Pipeline Button used to clear data from Tally API server to SubMenu
	
;Adding new report	
[Report : ExeLoc]
	Title: Process Voucher With AccuVelocity
	Form : ExeLocForm
	Add	: Variable	: vCurrentPath
	Set	: vCurrentPath	: If $$IsEmpty:$Fpath Then "C:\Program Files\TallyPrime" Else $$GetParentDirectory:$Fpath ;setting Path where the folder will open by defult

;Adding new Form	
[Form : ExeLocForm]
	Part : ExeLocPart,ExeLocPart3,ExeLocPart2
	Width: 80% page
	Height:40% page
	Add:Button:CallExe
	Add:Button:ClearPipeLine ;Adding Clear Pipeline Button used to clear data from Tally API server to SubMenu
	
;Adding new Part to Display Only 3 Lines - Exe Path, Select File and Path of Selected file	
[Part : ExeLocPart]
	Line : TopLine1,ExeLocLine3,ExeLocLine13,ExeLocLine,ExeLocLine2
	Border:Thick Bottom
	Height:18% page
[Line:TopLine1]
	Field:Form SubTitle
	Local:Field:Form SubTitle:Set as: "AccuVelocity AI"
	;Local:Field:Form SubTitle:Background:lightblue 
;Adding New Lines - Select File
[Line:ExeLocLine]
	Field:Medium prompt ,ExeLocField
	Local:Field:Medium prompt : Set as:"Select File"
	
;Adding New Lines - Path of Selected file	
[Line:ExeLocLine2]
	Field:Long prompt,ExeLocFieldup
	Local:Field:Long prompt:Set as:"Selected File Path To Process"
	
;Adding New Lines - Exe Path
[Line:ExeLocLine3]
	Field:Medium prompt ,Exe StaticPath
	Local:Field:Medium prompt : Set as:"AccuVelocity Exe"
	
[Line:ExeLocLine13]
	Field:Long prompt,TypeOfVChAV
	Local:Field:Long prompt:Set as:"Selecte Voucher type"
;[Line:ExeLocLine13]
;	Field:Long prompt,TempDisplayFile
;	Local:Field:Long prompt:Set as:"Selecte Voucher type"
;Adding New Field to Select File
[Field : ExeLocField]
	Use:file selection template
	Storage:AVExeFilePath
	Align:Left
	Width:50% Page
	
;[Field : TempDisplayFile]
;	Use:Name field
;	Set as: #ExeStaticPath + '"' + $Fpath + '"' ;Replace With AVVoucherProcess.exe file path
;	Set Always:Yes 								
;	Align:Left
;	Width:50% Page
	
;Adding New Field display Path to EXE
[Field : Exe StaticPath]
	Use:Name field
	Set as: "H:\Customers\TDL Development\Unzip this Pack\AVDocProcessor.exe" ;Replace With AVVoucherProcess.exe file path
	Set Always:Yes
	Skip:On  								;Does not allow us to go on that Field
	Align:Left
	Width:50% Page
	Read Only:Yes							;Does not allow us to edit that Field

;Adding New Field display Path of Selected file
[Field : ExeLocFieldup]
	Use:Name field
	Set as: If $$IsEmpty:#ExeLocField Then ##FileLoc Else $$GetFullPath:#ExeLocField ;Showing Value of Field EXELocField
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Storage:Fpath
	Width:50% Page
	
[Field : TypeOfVChAV]
	Use:Name field
	Set as: If $$IsEmpty:$$Value then "Purchase With Inventory" else $$Value ;Showing Value of Field EXELocField
	Set Always:Yes
	Align:Left
	Show Table:Always
	Storage:InvoiceType
	Table:TyAV
	Width:50% Page
	
[Table : TyAV]
	Title : $$LocaleString:"Type of Vouchers"
	List Name : Purchase With Inventory,Delivery Note,Journal Entry
	Format : $Name ,20
	

;Adding Button to call Exe
[Button:CallExe]
	Key:Ctrl+V
	Title:Process Voucher
	;ActionEx:Call:Call:DecideVoucherType
	ADD:Action:Call:DecideVoucherType
	;ActionEx:Log:Write File:"DEvoi"
	;ActionEx:Browse Url:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV ;Passing AVVoucherProcess.exe "FullPathOfFileToBeProcessed"
	
;creating a UDF to StoreValue of Seleceted File Path
[System:udf]
	AVExeFilePath:String:22445
	Fpath:String:22446
	InvoiceType:String:22447
;A defatult value to Exe to store Path
[Variable: FileLoc]
	Default		:"D:\Customer\REAL\TallyAccuVelocity\AVDocProcessor.exe"
	Type		: String
	Persistent	: Yes

;A Variable to store defatult 
[System: Variable]
	Variable	: FileLoc
	
[Function: DecideVoucherType]
	Variable: SelectedVoucherTypeAV:String
	Variable: cmdstr :String
	01:If: #TypeOfVChAV = "Purchase With Inventory"
		02:Set:SelectedVoucherTypeAV : " --purchase-with-inventory"
		03:Log:##SelectedVoucherTypeAV
		04:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
		05:Log:##cmdstr
		06:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV 
		07:Return
	08:Else
		09:If: #TypeOfVChAV = "Delivery Note"
			10:Set:SelectedVoucherTypeAV :" --delivery-note"
			11:Log:##SelectedVoucherTypeAV
			12:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
			13:Log:##cmdstr
			14:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV 
			15:Return
		16:Else:
			17:If:#TypeOfVChAV = "Journal Entry"
				18:Set:SelectedVoucherTypeAV :" --Journal"
				19:Log:##SelectedVoucherTypeAV
				20:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
				21:Log:##cmdstr
				22:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV 
				23:Return
			24:End If
		25:End If
		;12:MSGBOX:"VAl":##SelectedVoucherTypeAV
	26:End If
	
;Adding new Part to Display Step
[Part : ExeLocPart3]
	Line:ExeLocLine11
;	Background:LightBlue
;	Height:20% page
	
[Part : ExeLocPart2]
	Line:ExeLocLine4,ExeLocLine5,ExeLocLine6,ExeLocLine7,ExeLocLine8,ExeLocLine9,ExeLocLine10,ExeLocLine12
	Background:LightBlue	
[Line:ExeLocLine11]
	Field:UserMsgFeild8
[Line:ExeLocLine12]
	Field:UserMsgFeild9,UserMsgFeild10
[Line:ExeLocLine4]
	Field:short prompt ,UserMsgFeild
	Local:Field:short prompt  : Set as:"Step 1 :"
[Line:ExeLocLine5]
	Field:short prompt  ,UserMsgFeild2
	Local:Field:short prompt  : Set as:"Step 2 :"
[Line:ExeLocLine6]
	Field:short prompt ,UserMsgFeild3
	Local:Field:short prompt  : Set as:"Step 3 :"
[Line:ExeLocLine7]
	Field:short prompt  ,UserMsgFeild4
	Local:Field:short prompt : Set as:"Step 4 :"
[Line:ExeLocLine8]
	Field:short prompt  ,UserMsgFeild5
	Local:Field:short prompt  : Set as:"Step 5 :"
[Line:ExeLocLine9]
	Field:short prompt ,UserMsgFeild6
	Local:Field:short prompt : Set as:"Step 6 :"
[Line:ExeLocLine10]
	Field:short prompt ,UserMsgFeild7
	Local:Field:short prompt : Set as:"Step 7 :"
	
[Field : UserMsgFeild]
	Use:Narration Field
	Set as:"Ensure that your executable (EXE) file is located in the specified path provided in the AccuVelocity EXE field."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild2]
	Use:Narration Field
	Set as:"Select the type of voucher you would like to process from the available options."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild3]
	Use:Narration Field
	Set as:"Select the file you wish to process using AccuVelocity AI."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild4]
	Use:Narration Field
	Set as:"Verify that the full path of the selected file appears in the 'Selected File Path To Process' field."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild5]
	Use:Narration Field
	Set as:"Press Ctrl + V or click the Process Voucher button to send the selected file to AccuVelocity for processing."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild6]
	Use:Narration Field
	Set as:"Repeat the same steps for additional files or quit once the processing is complete."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild7]
	Use:Narration Field
	Set as:"You may exit the application once processing has started. A notification email will be sent once the data has been successfully received."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:70% Page
	Skip:On
[Field : UserMsgFeild8]
	Use:AsRightSubTitle
	Set as:"Guidelines for Using the AccuVelocity Voucher Process:"
	Set Always:Yes
	Align:Left
	Style:normal Bold
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild9]
	Use:AsRightSubTitle
	Set as:"Version - 2 Beta"
	Set Always:Yes
	Align:Left
	Style:normal Bold
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild10]
	Use:AsRightSubTitle
	Set as:"Date - 22nd January 2025"
	Set Always:Yes
	Align:Right
	Style:normal Bold
	Read Only:Yes
	Width:50% Page
	Skip:On

