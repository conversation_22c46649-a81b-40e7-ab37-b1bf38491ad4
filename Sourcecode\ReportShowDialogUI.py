import os
import pandas as pd
from PyQt5.QtWidgets import (
    Q<PERSON>oxLayout,
    QLabel,
    QDialog,
    QApplication,
)

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QLabel, QTableWidget, QTableWidgetItem, QPushButton, QHBoxLayout, QListWidget, QMessageBox
)
from PyQt5.QtGui import QIcon, QColor
from PyQt5.QtCore import Qt
import pandas as pd
import os
import sys
sys.path.append("")
from CustomHelper import CArgumentParser, CDocument, CGeneralHelper
import zipfile

class CSVReportDialog(QDialog):
    def __init__(self, csv_path, message, parent=None):
        super().__init__(parent)

        # Set window title and size
        self.setWindowTitle("CSV Report Viewer")
        self.resize(700, 500)

        # Enable all window buttons (Close, Minimize, Maximize)
        self.setWindowFlags(Qt.Window | Qt.WindowTitleHint | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)


        # Set window icon
        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        strIconPath = os.path.join(strResourceDirpath, "AvLogo.ico")
        self.setWindowIcon(QIcon(strIconPath))

        # Create the layout
        main_layout = QVBoxLayout(self)

        # Add the message at the top
        message_label = QLabel(message, self)
        message_label.setWordWrap(True)
        main_layout.addWidget(message_label)

        # Add the table widget to display the CSV data
        self.table_widget = QTableWidget(self)
        main_layout.addWidget(self.table_widget)

        # Load the CSV data into the table widget
        self.load_csv_data(csv_path)

        # Add buttons at the bottom
        button_layout = QHBoxLayout()
        button_layout.addStretch()  # Center-align buttons

        # "View Report" Button
        self.view_report_button = QPushButton("Open Report Directory", self)
        self.view_report_button.clicked.connect(lambda: self.open_file_explorer(csv_path))
        button_layout.addWidget(self.view_report_button)

        # "Close" Button
        self.close_button = QPushButton("Close", self)
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)

        button_layout.addStretch()  # Center-align buttons
        main_layout.addLayout(button_layout)

    def load_csv_data(self, csv_path):
        try:
            # Read the CSV file into a pandas DataFrame
            df = pd.read_csv(csv_path)

            # Set the table dimensions
            self.table_widget.setRowCount(len(df))
            self.table_widget.setColumnCount(len(df.columns))
            self.table_widget.setHorizontalHeaderLabels(df.columns)

            # Make the table read-only
            self.table_widget.setEditTriggers(QTableWidget.NoEditTriggers)

            # Customize header
            header = self.table_widget.horizontalHeader()
            header.setStyleSheet("background-color: #f0f0f0; font-size: 14px; font-weight: bold;")
            header.setDefaultAlignment(Qt.AlignCenter)
            header.setStretchLastSection(False)
            
            # Enable word wrap for the table
            self.table_widget.setWordWrap(True)

            # Populate the table with data from the DataFrame
            for row_idx, row_data in df.iterrows():
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    # Use justified alignment for text
                    item.setTextAlignment(Qt.AlignJustify | Qt.AlignVCenter)
                    # Make item not editable
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    # Alternate row colors for better readability
                    if row_idx % 2 == 0:
                        item.setBackground(QColor("#ffffff"))  # White background for even rows
                    else:
                        item.setBackground(QColor("#f5f5f5"))  # Light gray background for odd rows
                    self.table_widget.setItem(row_idx, col_idx, item)

            # First auto-adjust columns based on content
            self.table_widget.resizeColumnsToContents()
            
            # Then apply max width of 400px to any column that exceeds it
            max_width = 400
            for col in range(self.table_widget.columnCount()):
                current_width = self.table_widget.columnWidth(col)
                if current_width > max_width:
                    self.table_widget.setColumnWidth(col, max_width)
                elif current_width < 100:  # Set minimum width for very narrow columns
                    self.table_widget.setColumnWidth(col, 100)
            
            # Adjust row heights to fit wrapped content
            self.table_widget.resizeRowsToContents()
            
            # Add a little extra height to rows for better readability
            for row in range(self.table_widget.rowCount()):
                current_height = self.table_widget.rowHeight(row)
                self.table_widget.setRowHeight(row, current_height + 5)
            
            # Set vertical header visible for better row identification
            self.table_widget.verticalHeader().setVisible(True)

        except Exception as e:
            print(f"Error loading CSV file: {e}")

    def open_file_explorer(self, csv_path):
        """
        Open the file explorer to the directory containing the CSV file.
        """
        try:
            # Ensure the path is formatted correctly
            directory = os.path.dirname(csv_path)
            directory = os.path.normpath(directory)  # Normalize the path
            
            # Check if the directory exists
            if os.path.exists(directory):
                os.startfile(directory)  # Open the directory in the file explorer
            else:
                print(f"Directory not found: {directory}")
                # Optional: Show an error message box to the user
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(self, "Error", f"Directory not found: {directory}")
        except Exception as e:
            print(f"Failed to open directory: {e}")
            # Optional: Show an error message box to the user
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Error", f"Failed to open directory: {e}")


def show_csv_report_dialog(csv_path, message):
    # app = QApplication(sys.argv)

    # Create the dialog and show it
    dialog = CSVReportDialog(csv_path, message)
    dialog.exec_()


class ExcelReportDialog(QDialog):
    def __init__(self, excel_paths, message, parent=None):
        super().__init__(parent)

        # Set window title and size
        self.setWindowTitle("AccuVelocity Activity Log")
        self.resize(700, 200)
        
        # Enable window buttons
        self.setWindowFlags(Qt.Window | Qt.WindowTitleHint | Qt.WindowCloseButtonHint |
                            Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)
        
        # Set window icon (Modify path accordingly)
        strResourceDirpath = CGeneralHelper.MSGetResourceDirectory()
        strIconPath = os.path.join(strResourceDirpath, "AvLogo.ico")
        self.setWindowIcon(QIcon(strIconPath))

        # Create the layout
        main_layout = QVBoxLayout(self)

        # Add the message at the top
        message_label = QLabel(message, self)
        message_label.setWordWrap(True)
        main_layout.addWidget(message_label)

        # Create a list widget to display Excel file paths
        self.list_widget = QListWidget(self)
        for path in excel_paths:
            self.list_widget.addItem(path)
        main_layout.addWidget(self.list_widget)

        # Add buttons at the bottom
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # "Show Report" Button
        self.show_report_button = QPushButton("Show Reports", self)
        self.show_report_button.clicked.connect(self.open_selected_report)
        button_layout.addWidget(self.show_report_button)

        # "Close" Button
        self.close_button = QPushButton("Close", self)
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        button_layout.addStretch()
        main_layout.addLayout(button_layout)

    def open_selected_report(self):
        """Opens the selected Excel report file."""
        selected_item = self.list_widget.currentItem()
        if selected_item:
            file_path = selected_item.text()
            os.startfile(file_path)  # Open the file in the default application
        else:
            QMessageBox.warning(self, "No Selection", "Please select a report to open.")
