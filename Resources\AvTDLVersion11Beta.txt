;OM ;<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>

;Author <PERSON> <PERSON><PERSON><PERSON> | Owner - AccuVelocity | Version - 8 | Date - 09th May 2025

;DefVoucherType:"Delivery Note" ;"Purchase With Inventory"

[System: Variable] ; Varibles which can be used accros the Application
ConfigFilePath:$$SysInfo:ApplicationPath + "\Config.XML" ; Path to Config.xml - Expected to be parallel to Tally.exe \ Tally Application path eg: C:\Program Files\TallyPrime (2)
;Config.XML Contains Below Variables this are dyanamic values that changes for each user
BatchFilePAth:$$CollectionField:$EXPORTBATFile:1:ConfigVarsColl  ; Server Pipeline Clear path Used for APOSSSSSSSSI Currently used very less
BackUpDestinationPathLoc : $$CollectionField:$BackUpDest:1:ConfigVarsColl ;Back Up Destination, Location Where Backups taken by AV Should be saved, genrally inside AccuVelocity folder
TallyComDataPath:$$CollectionField:$TallyComDataPath:1:Config<PERSON><PERSON>Coll ; Tally Company Data Path, Location Where Tally Data is Stored - Needed for BackUp
AVExefilePathLoc: $$CollectionField:$AVEXELOC:1:ConfigVarsColl ;AccuVelocity Exe Location, Generally inside AccuVelocity Folder
AVHelpFilePath:$$CollectionField:$HelpHTMLPath:1:ConfigVarsColl ;Path to AccuVelocity Help doc
EnableStockItemInvExport: $$CollectionField:$EnableStockItemExport:1:ConfigVarsColl ;Boolean [Yes/No] indicating if stockitems needs to be exported or not
VOUCHERTYPEDEF:$$CollectionField:$VOUCHERTYPEDEF:1:ConfigVarsColl ;When Processing Voucher What should be the default selected voucher type [Purchase With Inventory,Delivery Note,Journal Entry,Purchase Without Inventory,Bank Statement,Receipt Note,Purchase Order]
AVfeatureXLPath:$$CollectionField:$AVfeatureXLPath:1:ConfigVarsColl ; Path to Listed Vendors.html file
;IsMultiVendor:$$CollectionField:$IsMultiVendor:1:ConfigVarsColl

[Collection:XMLDataColl]  ; Creating Collection and Reading Data from XML file path
	Data Source:File XML: ##ConfigFilePath  
	Fetch:*.*
	
[Collection: ConfigVarsColl]  ; Iterating through above Collection and initializing Config variables
	Source Collection	: XMLDataColl
	Walk	: CONFIGVARS
	Fetch	: *
	Compute	: AVEXELOC	: $AVEXELOC
	Compute	: EXPORTBATFile	: $EXPORTBATFile
	Compute	: VOUCHERTYPEDEF	: $VOUCHERTYPEDEF
	Compute	: BackUpDest : $BackUpDest
	Compute	: TallyComDataPath : $TallyComDataPath
	Compute	: EnableStockItemExport : $EnableStockItemExport
	Compute : AVHelpFilePath : $HelpHTMLPath
	Compute : AVfeatureXLPath : $AVfeatureXLPath
	;Compute	: IsMultiVendor	: $IsMultiVendor
	
; Passing Values from Config to System variables mentioned above
[Variable: ConfigFilePath] 
Type : String
Default: $$SysInfo:ApplicationPath + "\Config.XML"
[Variable: BatchFilePAth]
Type : String
Default: $$CollectionField:$EXPORTBATFile:1:ConfigVarsColl
;Persistent : Yes
[Variable: BackUpDestinationPathLoc]
Type : String
Default : $$CollectionField:$BackUpDest:1:ConfigVarsColl
;Persistent : Yes
[Variable: AVExefilePathLoc]
Type : String
Default:$$CollectionField:$AVEXELOC:1:ConfigVarsColl
[Variable: AVHelpFilePath]
Type : String
;Persistent : Yes
Default:$$CollectionField:$HelpHTMLPath:1:ConfigVarsColl
[Variable: AVfeatureXLPath]
Type : String
Default:$$CollectionField:$AVfeatureXLPath:1:ConfigVarsColl
[Variable: TallyComDataPath]
Type : String
Default:$$CollectionField:$TallyComDataPath:1:ConfigVarsColl
;Persistent : Yes
[Variable: EnableStockItemInvExport]
Type : String
Default:$$CollectionField:$EnableStockItemExport:1:ConfigVarsColl
[Variable: TakeBackUp]
Type : Logical
Default:Yes
[Variable: VOUCHERTYPEDEF]
Type : String
Default:$$CollectionField:$VOUCHERTYPEDEF:1:ConfigVarsColl
;[Variable: IsMultiVendor]
;Type : String
;Default:$$CollectionField:$IsMultiVendor:1:ConfigVarsColl
;Persistent : Yes
;[Variable: DefVoucherType]
;Type : String
;Persistent : Yes
;DefVoucherType:"Delivery Note"


;Clear Pipeline Button used to clear data from Tally API server
[Button:ClearPipeLine]
	Key:Ctrl+F
	;Action:Call:SetVarValueAs
	Title:Finish Import  ;Button Name
	Action:Call:CallClearPipeLine ;Function Call
	;Action:Browse Url Ex:##BatchFilePAth ;Replace With AVPipelineClear bat file path
	;Action:Browse Url Ex:$ClearPiplineBatPath;Replace With AVPipelineClear bat file path -- TODO---
	
; Help Display Button
[Button:ShowHelpDoc]
	Key:Ctrl+H 
	Title:AccuVelocity Help ;Button Name
	Action:Call:CallAVShowHelp ;Function Call
	;Action:Browse Url Ex:##AVHelpFilePath
	
;Takes Backup for given Company	
[Button:TakeBackUp]
	Key:Ctrl+N
	Title:BackUp ;Button Name
	Action:Call:Backup Company AV ;Function Call
	;Inactive: NOT $BTakeBackup
	Inactive:NOT #BackUpField ; Make this inactive When Backup feild is No
	
	
;Adding Clear Pipeline Button to Tally
[#Menu:Gateway of Tally]
	Buttons:ClearPipeLine
	
;Adding SubMenu of AccuVelocity to Tally
[#Menu : Gateway of Tally]
	
	Add:Key Item:After:@@locIndentUtilities: AccuVelocityAI:V:Menu:AccuVelocity AI
	Add:Indent:After:@@locIndentUtilities:AccuVelocityAI
	
;Adding Items to SubMenu of AccuVelocity to Tally
[Menu: AccuVelocityAI] 
	Add:Indent:After:Indent:Process
	Add:Item:AccuVelocity Voucher Processing:Create:ExeLoc  ; Openning Voucher Processing Report of AccuVelocity
	Add:Item:AccuVelocity Manual Import:Call:CallAVimportmanually   ; Call Manual import function
	Add:Button:ClearPipeLine ;Adding Clear Pipeline Button used to clear data from Tally API server to SubMenu
	Add:Button:ShowHelpDoc ;Adding Show Help Button used to clear data from Tally API server to SubMenu
	Add:Item:AccuVelocity Activity Logs:Call:CallAvActivityLogs ; Call Show activity Log function to display AV Logs
	Add:Item:AccuVelocity Help:Call:CallAVShowHelp ; Call Show help function to display AV Help
	Add:Indent:Config   ; Adding a Indent
	Add:Item:AccuVelocity Feature List:Call:CallAVFeatureList ; Call Show Av Featurelist button to display features
	Add:Item:AccuVelocity Run Diagnostics:Call:CallAVrundiagnostics ; Call Run Dignostics function
	Add:Item:AccuVelocity Backup Report Generate:Call:CallAVBackupReportGenerate ; Call Backup Report Generate function
	;Add:Indent:After:Indent:Info 
	;Add:Item:AccuVelocity Help:Browse URL:##AVHelpFilePath
	 
;Adding new report	
[Report : ExeLoc]
	Title: Process Voucher With AccuVelocity
	Form : ExeLocForm
	;Adding all needed varibles from system level to Report level and initializing them in report
	Add	: Variable	: vCurrentPath,BatchFilePAth,AVExefilePathLoc,EnableStockItemInvExport,BackUpDestinationPathLoc,TallyComDataPath,VOUCHERTYPEDEF ;,IsMultiVendor 
	Set	: vCurrentPath	: If $$IsEmpty:$Fpath Then "C:\Program Files\TallyPrime" Else $$GetParentDirectory:$Fpath ;setting Path where the folder will open by defult
	Set : BatchFilePAth:$$CollectionField:$EXPORTBATFile:1:ConfigVarsColl
	Set : AVExefilePathLoc: $$CollectionField:$AVEXELOC:1:ConfigVarsColl
	Set	: EnableStockItemInvExport: $$CollectionField:$EnableStockItemExport:1:ConfigVarsColl
	Set	: BackUpDestinationPathLoc : $$CollectionField:$BackUpDest:1:ConfigVarsColl
	Set	: TallyComDataPath:$$CollectionField:$TallyComDataPath:1:ConfigVarsColl
	Set : VOUCHERTYPEDEF:$$CollectionField:$VOUCHERTYPEDEF:1:ConfigVarsColl
;	Set	: IsMultiVendor:$$CollectionField:$IsMultiVendor:1:ConfigVarsColl
	
 
;Adding new Form	
[Form : ExeLocForm]
	Part : ExeLocPart,ExeLocPart3,ExeLocPart2
	Width: 80% page
	Height:40% page
	Add:Button:CallExe ; Adding Process Voucher Button 
	Add:Button:ClearPipeLine ;Adding Clear Pipeline Button used to clear data from Tally API server to SubMenu
	Add:Button:TakeBackUp ; Adding Take backup fucntion
	Add:Button:ShowHelpDoc ; Adding Take backup fucntion
	On:Form Accept:Yes:Call:ProcessALL ; on Ctrl+A (Accept) Calling main function processing voucher that calls AV Exe
	Persist:True ; Making this TRUE to ensure Processing of vaoucher is happening even after calosing the report
	;On:Before Export:Yes: Call: SetVarsColl
;	No Confirm:True
;	Confirm Text:"TRw"

;Adding new Part to Display Lines - Exe Path, Select File and Path of Selected file	, Voucher type, Back Up needed, Multiple Voucher
; Add Line in Vertical order eg if you want line18 before line12 keep it ahead of it
[Part : ExeLocPart]
	Line : TopLine1,ExeLocLine3,ExeLocLine15,ExeLocLine,ExeLocLine2,ExeLocLine18,ExeLocLine12 ;ExeLocLine13 ExeLocLine14 - BackUp 
	;Scroll:Horizontal
	;Repeat:ExeLocLine3:CDCollection
	Border:Thick Bottom
	Height:16% page
	
;[Part : ExeLocPart4]
;	Line:Templine
;	Repeat:Templine:CDCollection
	
[Line:TopLine1] ; Adding Title on top of form
	Field:Form SubTitle  ; only local Field used 
	Local:Field:Form SubTitle:Set as: "AccuVelocity AI"
	;Local:Field:Form SubTitle:Background:lightblue 
	
;Adding New Lines - Select File
[Line:ExeLocLine]
	Field:Medium prompt ,ExeLocField ; Local Field and Our created Field also added in Horizontal Order  
	Local:Field:Medium prompt : Set as:"Select File"
; Adding line BackUp before import
[Line:ExeLocLine12]
	Field:Long prompt,BackUpField ; Local Field and Our created Field also added in Horizontal Order  
	Local:Field:Long prompt : Set as:"BackUp Before Import:"
; Adding line Multi Vendor import	
[Line:ExeLocLine18]
	Field:Long prompt,MultiVendorField ; Local Field and Our created Field also added in Horizontal Order  
	Local:Field:Long prompt : Set as:"Multi Vendor:"
; Adding line Backup Destination unused Currently
[Line:ExeLocLine13]
	Field:Medium prompt,BackUpDestinationfolderSelection ; Local Field and Our created Field also added in Horizontal Order  
	Local:Field:Medium prompt: Set as: "BackUp Destination"
	;Local:Field:Medium prompt:Inactive:NOT $BTakeBackup
	Local:Field:Medium prompt:Inactive:NOT #BackUpField
; Adding line Selected Backup Destination unused Currently	
[Line:ExeLocLine14]
	Field:Long prompt,ValueOfBackUPPath ; Local Field and Our created Field also added in Horizontal Order  
	Local:Field:Long prompt: Set as: "Selected BackUp Destination"
	;Local:Field:Long prompt:Inactive:NOT $BTakeBackup
	Local:Field:Medium prompt:Inactive:NOT #BackUpField
; Adding line Voucher type	
[Line:ExeLocLine15]
	Field:Long prompt,TypeOfVChAV ; Local Field and Our created Field also added in Horizontal Order  
	Local:Field:Long prompt:Set as:"Selecte Voucher type"
	
;Adding New Lines - Path of Selected file	
[Line:ExeLocLine2]
	Field:Long prompt,ExeLocFieldup ; Local Field and Our created Field also added in Horizontal Order  
	Local:Field:Long prompt:Set as:"Selected File Path To Process"
	
;Adding New Lines - Exe Path
[Line:ExeLocLine3]
	Field:Medium prompt ,Exe StaticPath ; Local Field and Our created Field also added in Horizontal Order  
	Local:Field:Medium prompt : Set as:"AccuVelocity Exe"

;Adding New Field to Select File
[Field : ExeLocField]
	Use:file selection template
	Storage:AVExeFilePath
	Align:Left
	Width:50% Page
; Field showing value of Back up Boolean [Yes or No]	
[Field : BackUpField]
	Use:Logical Field
	;Type:Logical
	Show Table: Always
	Set Always:Yes
	Set as:Yes
	;Storage: BTakeBackup
	Align:Left
	Width:50% Page
	
; Field showing value of multivendor Boolean [Yes or No]	
[Field : MultiVendorField]
	Use:Logical Field
	Show Table: Always
	Set Always:Yes
	Set as:No
	Align:Left
	Width:50% Page
	
; Field showing value of Backup Destination 
[Field : BackUpDestinationfolderSelection]
	Set as:##BackUpDestinationPathLoc+ "\" + @@DateForm + "\" + @@TimeForm  ;Getting Location and Appending Dates
	;Set as:"H:\Temp" + + "\" + @@DateForm + "\" + @@TimeForm
	Use:Name Field
;	Use: Folder selection template
	Storage:AVBackUpDir
	Align:Left
	Width:50% Page
	;Inactive: NOT $BTakeBackup
	Inactive:NOT #BackUpField
	
; Field showing value of Backup Destination path 	
[Field : ValueOfBackUPPath]
	Use:Name Field
	Set as: If $$IsEmpty:#BackUpDestinationfolderSelection Then "" Else #BackUpDestinationfolderSelection  + "\" + @@DateForm + "\" + @@TimeForm
	Set Always:Yes
	Align:Left
	Width:50% Page
	;Inactive: NOT $BTakeBackup
	Inactive:NOT #BackUpField
	Read Only:Yes
	
;Adding New Field display Path to EXE
[Field : Exe StaticPath]
	Use:Name field
	Set as: ##AVExefilePathLoc
	;Set as: "D:\Customer\REAL\TallyAccuVelocity\AVDocProcessor.exe" ;Replace With AVVoucherProcess.exe file path
	;Set as:$$CollectionField:$AVExeLocation:1:CDCollection ;Replace With AVVoucherProcess.exe file path
	Set Always:Yes
	Skip:On  								;Does not allow us to go on that Field
	Align:Left
	Width:50% Page
	Read Only:Yes							;Does not allow us to edit that Field

;Adding New Field display Path of Selected file
[Field : ExeLocFieldup]
	Use:Name field
	Set as: If $$IsEmpty:#ExeLocField Then ##FileLoc Else $$GetFullPath:#ExeLocField ;Showing Value of Field EXELocField
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Storage:Fpath
	Width:50% Page
	
; Field showing value of Voucher type 	
[Field : TypeOfVChAV]
	Use:Name field
	Set as: If $$IsEmpty:$$Value then ##VOUCHERTYPEDEF else $$Value
	;Set as: If $$IsEmpty:$$Value then "Purchase With Inventory" else $$Value ;Showing Value of Field EXELocField
	;Set as: If $$IsEmpty:$$Value then $$CollectionField:$VOUCHERTYPEDEF:1:CDCollection else $$Value ;Showing Value of Field EXELocField
	Set Always:Yes
	Align:Left
	Show Table:Always ;To show Dropdown table of avialble voucher type
	Storage:InvoiceType
	Table:TyAV ;Adding Table Values
	Width:50% Page
	
;Table Values for Avilable Voucher type	
[Table : TyAV]
	Title : $$LocaleString:"Type of Vouchers"
	List Name :Purchase With Inventory,Delivery Note,Journal Entry,Purchase Without Inventory,Bank Statement,Receipt Note,Purchase Order
	Format : $Name ,20
	

;Adding Button to call Exe
[Button:CallExe]
	Key:Ctrl+P
	Title:Process Voucher
	ADD:Action:Call:DecideVoucherType
	;Action:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' ;Passing AVVoucherProcess.exe "FullPathOfFileToBeProcessed"
	
;creating a UDF to StoreValue of Seleceted File Path
[System:udf]
	AVExeFilePath:String:22445
	Fpath:String:22446
	BTakeBackup:Logical:22447
	AVBackUpDir:String:22448
	InvoiceType:String:22449
	
;A defatult value to Exe to store Path
[Variable: FileLoc]
	Default		:##AVExefilePath
	Type		: String
	Persistent	: Yes
;A Variable to store defatult 
[System: Variable]
	Variable	: FileLoc
	
;Button for process All Calls Decide Voucher type and Call Backup
[Function: ProcessALL]
	01:If: #BackUpField = "Yes"
		02:Call:Backup Company AV
        ; 03:Call:AskOverwritePrompt		
		03:Call:DecideVoucherType
	04:Else:
		05:Call:DecideVoucherType
	06:End If

[Function: AskOverwritePrompt]
	; Variable: IsOverwriteExistBackup:String
    ;00: Set: IsOverwriteExistBackup: ""	 
	01 : QUERYBOX : "Do you want to overwrite your existing backup or make a new copy as daily limit of backup exceeds?" : Yes:No
	02 : If : $$LastResult
	    03 : Set: IsOverwriteExistBackup: "Yes"
	04 : Else :
	    05 : Set: IsOverwriteExistBackup: "No"
	06 : End If 
	07 :Log:##IsOverwriteExistBackup ; Logs CmdStr

;Fucntion that decides Voucher type, Creates Command String and Calls AccuVelocity.Exe
;Important to follow Numbers (STEPS)
[Function: DecideVoucherType]
	Variable: SelectedVoucherTypeAV:String
	Variable: cmdstr :String
	01:If: #TypeOfVChAV = "Purchase With Inventory"  ;Checks if Voucher Type is Puchase with inventory
		02:Set:SelectedVoucherTypeAV : " --purchase-with-inventory"  ;Add it's Flag
		03:Log:##SelectedVoucherTypeAV ; Logs it 
			04:If :##EnableStockItemInvExport ; Checks If item EXport is {YES or NO}
				05:If:#MultiVendorField ; Checkes if MultiVendor is [YES or NO]
					06:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor" ; Sets Value for Variable Cmdstr based on condiftions
					07:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor" ; Call AccuVelocity EXE Value for Variable Cmdstr based on condiftions
				08:Else
					09:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" ; Sets Value for Variable Cmdstr based on condiftions is called when multivendor is disbaled
					10:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" ; Call AccuVelocity EXE Value for Variable Cmdstr based on condiftions is called when multivendor is disbaled
				11:End If
			12:Else
				13:If:#MultiVendorField
					14:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --multiple-vendor" ; Sets Value for Variable Cmdstr based on condiftions is called when Stockitem export is disbaled
					15:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --multiple-vendor" ; Call AccuVelocity EXE Value for Variable Cmdstr based on condiftions is called when Stockitem is disbaled
				16:Else:
					17:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV ; Sets Value for Variable Cmdstr based on condiftions is called when Stockitem export and multi vendor both disbaled
					18:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV ; Call AccuVelocity EXE Value for Variable Cmdstr based on condiftions is called when Stockitem export and multi vendor both disbaled
				19:End If
			20:End If
		21:Log:##cmdstr ; Logs CmdStr
		22:Return
	23:Else
		24:If: #TypeOfVChAV = "Delivery Note"
			25:Set:SelectedVoucherTypeAV :" --delivery-note"
			26:Log:##SelectedVoucherTypeAV
			27:If :##EnableStockItemInvExport
				28:If:#MultiVendorField
					29:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor"
					30:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor"
				31:Else
					32:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
					33:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
				34:End If
			35:Else
				36:If:#MultiVendorField
					37:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --multiple-vendor"
					38:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --multiple-vendor"
				39:Else:
					40:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
					41:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV
				42:End If
			43:End If
			44:Log:##cmdstr
			45:Return
		46:Else:
			47:If:#TypeOfVChAV = "Journal Entry"
				48:Set:SelectedVoucherTypeAV :" --Journal"
				49:Log:##SelectedVoucherTypeAV
				50:If :##EnableStockItemInvExport
					51:If:#MultiVendorField
						52:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor"
						53:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor"
					54:Else
						55:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
						56:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
					57:End If
				58:Else
					59:If:#MultiVendorField
						60:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --multiple-vendor"
						61:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --multiple-vendor"
					62:Else:
						63:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
						64:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV
					65:End If
				66:End If
				67:Log:##cmdstr
				68:Return
			69: Else:
				70:If:#TypeOfVChAV = "Purchase Without Inventory"
					71:Set:SelectedVoucherTypeAV :" --purchase-without-inventory"
					72:Log:##SelectedVoucherTypeAV
					73:If :##EnableStockItemInvExport
						74:If:#MultiVendorField
							75:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor"
							76:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor"
						78:Else
							79:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
							80:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
						81:End If
					82:Else
						83:If:#MultiVendorField
							84:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV +" --multiple-vendor"
							85:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --multiple-vendor"
						86:Else:
							87:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
							88:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV
						89:End If
					90:End If
					91:Log:##cmdstr
					92:Return
				93: Else:
				; BANK Stmt Does not have Stockitem export or multivendor so just simply creating cmdstr
					94:If:#TypeOfVChAV = "Bank Statement"
						95:Set:SelectedVoucherTypeAV :" --bank-statement"
						96:Log:##SelectedVoucherTypeAV
	;						55:If :##EnableStockItemInvExport
	;							56:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
	;							57:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
	;						58:Else
						97:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
						98:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV
					100:Else:
						101:If:#TypeOfVChAV = "Receipt Note"
							102:Set:SelectedVoucherTypeAV :" --receipt-note"
							103:Log:##SelectedVoucherTypeAV
							104:If :##EnableStockItemInvExport
								105:If:#MultiVendorField
									106:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor"
									107:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor"
								108:Else
									109:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
									110:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
								111:End If
							112:Else
								113:If:#MultiVendorField
									114:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --multiple-vendor"
									115:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --multiple-vendor"
								116:Else:
									117:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
									118:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV
								119:End If
							120:End If
							121:Log:##cmdstr
							122:Return
						123:Else:
							124:If:#TypeOfVChAV = "Purchase Order"
							125:Set:SelectedVoucherTypeAV :" --purchase-order"
							126:Log:##SelectedVoucherTypeAV
							127:If :##EnableStockItemInvExport
								128:If:#MultiVendorField
									129:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor"
									130:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem" + " --multiple-vendor"
								131:Else
									132:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
									133:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
								134:End If
							135:Else
								136:If:#MultiVendorField
									137:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --multiple-vendor"
									138:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --multiple-vendor"
								139:Else:
									140:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
									141:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV
								142:End If
							143:End If
							144:Log:##cmdstr
							145:Return
						146:End If
					147:End If
				148:End If
			149:End If
			150:End If
		151:End If
		;12:MSGBOX:"VAl":##SelectedVoucherTypeAV
	152:End If
;Function Calling AccuVelocity.exe to show Activity logs
[Function: CallAvActivityLogs]
	Variable : AVactivityLogs: String
	05:	Set: AVExefilePathLoc:$$CollectionField:$AVEXELOC:1:ConfigVarsColl
	10: Set: AVactivityLogs : ##AVExefilePathLoc + ":"+ " --get-activity-report" ;Creating cmdstr
	11: Log: ##AVactivityLogs ;Loging cmdstr
	12: Browse Url:##AVExefilePathLoc:" --get-activity-report" ;calling exe
	
;Function to call Clear AV pipeline [Not Used Now]	
[Function: CallClearPipeLine]
	05:Set:BatchFilePAth:$$CollectionField:$EXPORTBATFile:1:ConfigVarsColl ;Creating Path
	06:Log:##BatchFilePAth ;Loging Path
	10:Browse Url Ex:##BatchFilePAth ;calling Bat file
	
;Function to show Help Docs	
[Function: CallAVShowHelp]
	05:Set:AVHelpFilePath:$$CollectionField:$HelpHTMLPath:1:ConfigVarsColl ;Creating Path
	06:Log:##AVHelpFilePath ;Loging Path
	10:Browse Url Ex:##AVHelpFilePath ;Opening HTML file
	
;Function show feature list	
[Function: CallAVFeatureList]
	05:Set:AVfeatureXLPath:$$CollectionField:$AVfeatureXLPath:1:ConfigVarsColl ;Creating Path
	06:Log:##AVfeatureXLPath ;Loging Path
	10:Browse Url Ex:##AVfeatureXLPath ;Opening HTML file
	
;Function Calling  AccuVelocity.exe to show Activity logs	
[Function: CallAVrundiagnostics]
	Variable : AVrundiagnostics: String 
	05:	Set: AVExefilePathLoc:$$CollectionField:$AVEXELOC:1:ConfigVarsColl
	10: Set: AVrundiagnostics : ##AVExefilePathLoc + ":"+ " --run-diagnostics" ;Creating cmdstr
	11: Log: ##AVrundiagnostics ;Loging cmdstr
	12: Browse Url:##AVExefilePathLoc:" --run-diagnostics" ;calling exe
	
;Function Calling  AccuVelocity.exe to show Activity logs
[Function: CallAVimportmanually]
	Variable : AVimportmanually: String
	05:	Set: AVExefilePathLoc:$$CollectionField:$AVEXELOC:1:ConfigVarsColl
	10: Set: AVimportmanually : ##AVExefilePathLoc + ":"+ " --manual-import-voucher" ;Creating cmdstr
	11: Log: ##AVimportmanually ;Loging cmdstr
	12: Browse Url:##AVExefilePathLoc:" --manual-import-voucher" ;calling exe

[Function: CallAVBackupTrackRecord]
	Variable : AVBackupTrackRecord: String
	05:	Set: AVExefilePathLoc:$$CollectionField:$AVEXELOC:1:ConfigVarsColl
	10: Set: AVBackupTrackRecord : ##AVExefilePathLoc + ":"+ " --track-backup-record" ;Creating cmdstr
	11: Log: ##AVBackupTrackRecord ;Loging cmdstr
	12: Browse Url:##AVExefilePathLoc:" --track-backup-record" ;calling exe

[Function: CallAVBackupReportGenerate]
	Variable : AVBackupReportGenerate: String
	05:	Set: AVExefilePathLoc:$$CollectionField:$AVEXELOC:1:ConfigVarsColl
	10: Set: AVBackupReportGenerate : ##AVExefilePathLoc + ":"+ " --generate-backup-report" ;Creating cmdstr
	11: Log: ##AVBackupReportGenerate ;Loging cmdstr
	12: Browse Url:##AVExefilePathLoc:" --generate-backup-report" ;calling exe
	
;Adding new Part to Display Step
;This is just basic static Structure to Create Steps Listed Below
[Part : ExeLocPart3]
	Line:ExeLocLine10
;	Background:LightBlue
;	Height:20% page
[Part : ExeLocPart2]
	Line:ExeLocLine4,ExeLocLine5,ExeLocLine6,ExeLocLine7,ExeLocLine19,ExeLocLine17,ExeLocLine8,ExeLocLine9,ExeLocLine16,ExeLocLine11
	Background:LightBlue	
[Line:ExeLocLine10]
	Field:UserMsgFeild7
[Line:ExeLocLine11]
	Field:UserMsgFeild8,UserMsgFeild9
[Line:ExeLocLine4]
	Field:short prompt ,UserMsgFeild
	Local:Field:short prompt  : Set as:"Step 1 :"
[Line:ExeLocLine5]
	Field:short prompt ,UserMsgFeild2
	Local:Field:short prompt  : Set as:"Step 2 :"
[Line:ExeLocLine6]
	Field:short prompt ,UserMsgFeild3
	Local:Field:short prompt  : Set as:"Step 3 :"
[Line:ExeLocLine7]
	Field:short prompt  ,UserMsgFeild4
	Local:Field:short prompt : Set as:"Step 4 :"
[Line:ExeLocLine19]
	Field:short prompt  ,UserMsgFeild53
	Local:Field:short prompt  : Set as:"Step 5 :"
[Line:ExeLocLine17]
	Field:short prompt  ,UserMsgFeild52
	Local:Field:short prompt  : Set as:"Step 6 :"
[Line:ExeLocLine8]
	Field:short prompt  ,UserMsgFeild5
	Local:Field:short prompt  : Set as:"Step 7 :"
[Line:ExeLocLine9]
	Field:short prompt ,UserMsgFeild6
	Local:Field:short prompt : Set as:"Step 8 :"
[Line:ExeLocLine16]
	Field:short prompt ,UserMsgFeild10
	Local:Field:short prompt : Set as:"Step 9 :"
	
[Field : UserMsgFeild]
	Use:Narration Field
	Set as:"Ensure that your executable (EXE) file is located in the specified path provided in the AccuVelocity EXE field."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild2]
	Use:Narration Field
	Set as:"Select the type of voucher you would like to process from the available options."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild3]
	Use:Narration Field
	Set as:"Select the file you wish to process using AccuVelocity AI."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild4]
	Use:Narration Field
	Set as:"Verify that the full path of the selected file appears in the 'Selected File Path To Process' field."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild53]
	Use:Narration Field
	Set as:"If selected file has more than one Vendor,choose Yes else No."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild52]
	Use:Narration Field
	Set as:"To back up the selected company before import, choose Yes in BackUpBeforeImport, or No if not."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild5]
	Use:Narration Field
	Set as:"Press Ctrl + V or Ctrl + A or click the Process Voucher button to send the selected file to AccuVelocity for processing."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:70% Page
	Skip:On
[Field : UserMsgFeild6]
	Use:Narration Field
	Set as:"Repeat the same steps for additional files or quit once the processing is complete."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild10]
	Use:Narration Field
	Set as:"You may exit the application once processing has started. A notification email will be sent once the data has been successfully received."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:70% Page
	Skip:On
[Field : UserMsgFeild7]
	Use:AsRightSubTitle
	Set as:"Guidelines for Using the AccuVelocity Voucher Process:"
	Set Always:Yes
	Align:Left
	Style:normal Bold
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild8]
	Use:AsRightSubTitle
	Set as:"Version - 8"
	Set Always:Yes
	Align:Left
	Style:normal Bold
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild9]
	Use:AsRightSubTitle
	Set as:"Date - 09th May 2025"
	Set Always:Yes
	Align:Right
	Style:normal Bold
	Read Only:Yes
	Width:50% Page
	Skip:On

; Formulas used for Backup to get today's date and time and company number
[System: Formula]
	
	CoNumber : $$String:($CmpNumStr:Company:##SVCurrentCompany):5
	DateForm : $$String:$$MachineDate
	TimeForm : @@HrsForm + @@MtsForm
	HrsForm : If $$StringPart:$$MachineTime:0:2 CONTAINS ":" +
	Then $$StringPart:$$MachineTime:0:1 +
	Else $$StringPart:$$MachineTime:0:2
	MtsForm : if $$StringPart:$$MachineTime:0:2 CONTAINS ":" +
	Then $$StringPart:$$MachineTime:2:2 +
	Else $$StringPart:$$MachineTime:3:2
	
; Function for Backup
[Function: Backup Company AV]
	; Backup needs 4 Args 
	; 1. Backup Destination
	; 2. Tally Company Data Destination
	; 3. Tally company name
	; 4. Tally company Number
	;Setting Variables like destination path and Company data path
	Variable : BackupDetVarAV: String 
	Variable: TallyDataPath : String: ##TallyComDataPath
	Variable: BackUpDest: String: ##BackUpDestinationPathLoc + "\" + @@DateForm + "\" + @@TimeForm ; Adding date time folder values to path
	;10 : SET : BackupDetVarAV : #ValueOfBackUPPath + ", " + ##SVCurrentPath + ", " + ##SVCurrentCompany + + ", " + @@CoNumber
	10 : SET : BackupDetVarAV : ##BackUpDest + ", " + ##TallyComDataPath + ", " + ##SVCurrentCompany + + ", " + @@CoNumber ; Creating Arg string for Backup Action in tally
	;20 : MSGBOX :"Return Value" : ##BackupDetVarAV
	;; loging Arg string for Backup Action in tally
	30 : LOG :##BackUpDest
	31 : Log:##TallyComDataPath
	32 : Log: ##SVCurrentCompany
	33 : Log: @@CoNumber
	34 : Log: ##BackupDetVarAV
	;35 : Log: $$CollectionField:$EXPORTBATFile:1:ConfigVarsColl
	40 : BACKUP COMPANY : ", " : ##BackupDetVarAV ; Calling BACKUP Actiong by passing needed Args
	41 : Call:CallAVBackupTrackRecord


;;;;;;;;;;;;;;;;;;;;;;;;;;;;;---------------------------------ATTECHMENT TDL-------------------------------------------;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

; Modifying Voucher report and Bottom left Part to add Attachment Boolean[Yes-No]
[#Part	:	VCH Narration]

		Add	:	Switch	:	BankDetRcpt	:	BankDet VCH Narration
		
; Modifying Voucher report and Bottom left Part to add Attachment Boolean[Yes-No]	
[!Part	:	BankDet VCH Narration]
	
		Add : 	Line 	: 	AVHyperlinkCompany

; Modifying Voucher report and Bottom left Part to add Attachment Boolean[Yes-No]
	
[Line	:	AVHyperlinkCompany]  
	
		Fields  	:	Short Prompt, AVHyperlinkCompany
    
	Local   : Field : Short Prompt : 	Info	: 	$$LocaleString:"Attatch Supporting Doc.?"
	Local   : Field : Short Prompt :	Color 	: 	Blue
    Local   : Field : Short Prompt : 	Width	:	25
	
; Modifying Voucher report and Bottom left Part to add Attachment Boolean[Yes-No]

[Field	:	AVHyperlinkCompany]
    
		Use         : Logical Field
		Set         : No 
		Color       : Blue
		Border      : Thin Bottom
		Sub Form    : AV Doc Path Details	: $$Value
;-----------------------------------------------------------------------------------------------------------------------------------------
; Report for Attachment
[Report: AV Doc Path Details]
	Title		: AccuVelocity Document Attach
	Form		: AV Doc Path Details ;Adding form
	
;Form for Attchament
[Form: AV Doc Path Details]

    Part        : FormSubTitle, AV Doc Path Details ; Adding parts
    FullWidth   : No
	Height		: 60% Screen
	Width		: 100% Screen;70% Screen
    Background  : "White"
    SpaceRight  : 0.5
    SpaceLeft   : 1.5
    Local       : Field : Form SubTitle : Info  : $$LocaleString:"Type The Path Of Document To Be Link" 
	Local       : Field : Form SubTitle : Color : Blue
    Option      : Small Size Form
	
;part for Attchament
[Part: AV Doc Path Details]
	
    Lines       : AV Doc PathTit, AV Doc Path Details 
    Repeat      : AV Doc Path Details   : LinkOfferedAgg ; iterating through UDF (UDF is like a list of dict)
    Break on    : $$IsEndOfList:#AVDocPathDetails ;;$LinkDocName 
	Scroll		: Vertical
	
;Line for Attchament	
[Line: AV Doc PathTit]
	; Adding Columns
	Use: AV Doc Path Details
	Local: Field: AV DocMulti SNo     : Set as : "S.No."
	Local: Field: Default          : Delete : Storage
	Local: Field: Default          : Delete : Inactive
	Local: Field: AV DocName          : Set as : "Document Name"
	Local: Field: AV DocPathFullPath  : Set as : "Document Location"
	Local: Field: AV Doc Path Details : Set as : "Doc. Full Path"
	Local: Field: AV DocView          : Set as : "View"   
	Local: Field: AV DocUploadtime    : Set as :  "Upload/Alter Date and Time"
	Local: Field: AV DocUserName      : Set as : "User Name"
	Local: Field: AV DocUserName      : Delete : Color     : Red 
	Local: Field: Default : Skip: Yes
	
; Adding fields from UDF to line 
[Line: AV Doc Path Details]
	
    Fields      : AV DocMulti SNo,AV DocName,AV Doc Path Details, AV TestFld , AV DocPathFullPath
	Right Fields: AV DocUploadtime,AV DocUserName
	
; fields from UDF path - File path
[Field: AV TestFld]
	
	Use			: Name Field
	Type		: String	: Forced
	Set as		: $TDL
	Storage		: TDL
	Set Always	: Yes
	Invisible	: Yes
	Skip		: Yes

; fields from UDF path - Sr Number
[Field: AV DocMulti SNo]
    Use         : Multi SNo Template
    Set as      : $$String:$$Line + "."
    Set always  : Yes
	Storage     : stDocNameLineno 
    Align       : Right
	Width       : 15 MMS 

; fields from UDF path - Doc name	
[Field: AV DocName]
    Use    	 	: Name Field
    Storage 	: LinkDocName
    Case        : Normal
	Set Always  : Yes 
	Width       : 50 MMS 
	
; fields from UDF path -	File Name	
[Field: AV DocPathFullPath] ;2
	
	Use				: Name Field
	Set as			: @@TDLFilePath 
	Set Always		: Yes
	Width			: 50
	Skip			: Yes
	Storage			: Link Offered
	
; fields from UDF Multi path select
[Field: AV Doc Path Details] ;1
	
	Use				: Multi File Selection Template
	;Use       : Name Field
	Set as			: $$GetFileNameFromPath:@@ActualTDLName ;; If $$Line>1 AND $$IsEmpty:$TDL Then $$SysName:EndofList Else +
						;;$$GetFileNameFromPath:@@ActualTDLName
	Common Table	: No
	Width			: 30
	Storage         : stFullpath
	
; fields from UDF - Upload time date
[Field: AV DocUploadtime]
	Use       : Name Field 
	Set as    : @DUDateTimeUpdation
	DUDateTimeUpdation	: "Upload on " + ($$String:@DUDateUpdation) + " at " + ($$SysInfo:SystemTime)
	DUDateUpdation		: $$SysInfo:SystemDate
	Storage   : StUploadTimePeriod 
	Set Always: Yes
	Inactive  : $$IsEmpty:$LinkDocName
	
	Width     : 50 MMS 
	
; fields from UDF - Str User Name	
[Field: AV DocUserName]
	Use       : Name Field 
	Set as    : ##SVUserName
	Storage   : stUserName
	Set Always: Yes
	Skip      : Yes
	Color     : Red 
	Inactive  : $$IsEmpty:$LinkDocName
	Width     : 30 MMS 

;button on top of voucher to open doc
[#Form:Voucher]
Add:Top Button:At Beginning:OpenDoc

;button on top of voucher to open doc
[Button:OpenDoc]
Title:$$LocaleString:"Open Attachment"
Key:Ctrl+O
Action:Sub Form:AVShowDocs

;Report to display Attchment
[Report:AVShowDocs]
	Form: AVShowDocsForm
;form to display Attchment
[Form:AVShowDocsForm]
	Part: AVShowDocsPart
	;FullWidth   : No
	Width		: 100% Screen
	Height		: 60% Screen
;part to display Attchment
[Part:AVShowDocsPart]
	Lines : AVShowDocsLineTitle,AVShowDocsLine ; adding lines
    Repeat : AVShowDocsLine  : Voucher_Doc ; iterating through UDF to display vouchers
	Scroll		: Vertical
	Bottom Line:AVUserNote ; adding note to bottom
	
; Columns for report
[Line:AVShowDocsLineTitle]
	Use: AVShowDocsLine
	Local:Field: AVSrNo:Set as : "Sr No."
	Local:Field: AVViewDocPath:Set as : "Document Name"
	Local: Field: AVView:Set as :"Document Full Path"
	Local: Field: AVDateTime : Set as :"Upload/Alter Date and Time"
	Local: Field: AV DocView : Set as :"View"
	Border: Bottom Thick Column Titles
; adding fields
[Line:AVShowDocsLine]
	Fields      : AVSrNo,AVViewDocPath,AVView,AVDateTime, AV DocView
; creating bottom note line
[Line:AVUserNote]
	Field:AVUserNoteField
; creating bottom note field
[Field:AVUserNoteField]
	;Use				:Name Field
	Style			:Normal Bold Italic
	Align			:Centre
	Set as			:"Please, Press 'Enter' to view the Document"
	Color			:Green
	Read Only		:Yes
	Skip			:Yes
; UDF field - Sr No
[Field: AVSrNo]
	Use				: Name Field
	Set as: $stDocNameLineno
	Width			: 30
	Read Only		: Yes
	Skip			: Yes
; UDF field - docName
[Field: AVViewDocPath]
	Use				: Name Field
	Set as: $LinkDocName
	Width			: 30
	Read Only		: Yes
	Skip			: Yes
; UDF field - doc full path
[Field: AVView]
	Set as: $TDL
	Use				: Name Field
	Width			: 50
	Read Only		: Yes
	Skip			: Yes
; UDF field - upload alter time
[Field: AVDateTime]
	Use				: Name Field
	Set as: $StUploadTimePeriod
	Width			: 50
	Read Only		: Yes
	Skip			: Yes
; UDF field - view
[Field: AV DocView]
	Use				: Name Field
	Set as			: "View"
	Color: Blue
	On:Accept:Yes:Browse Url:$TDL ; open attchment file 
	;On:Accept:Yes:Browse Url:#AVDocPathFullPath+ "\" + #AVDocPathDetails
	;Add:Action:Browse Url:$$GetFileFullPath:@@ActualTDLName
	Set Always		: Yes
	Width			: 50
	Read Only		: Yes
; Creating collaction from UDF
[Collection: Voucher_Doc]	
Source Collection :..		; ListOfDocs; ; to get For Curent voucher
Walk:LinkOfferedAgg			;Walk            : LedgerEntries, LinkOfferedAgg
Fetch             : *.*		;LinkDocName, stUserName  ; to fech all

; System UDF Values
[System: UDF]
	
	LinkOfferedAgg  : Aggregate : 1010
	Link Offered 	: String	: 1011
	LinkDocName     : String    : 1012 
	stDocNameLineno : String    : 1013 
	stfilenameList  : String    : 1014
	
	stAllowDMVtypYes   : Logical: 1015
	StUploadTimePeriod : String : 1016
	stUserName         : String : 1017
	
    stFullpath      : String    : 1018 
	
	tdl				: String	: 1019

