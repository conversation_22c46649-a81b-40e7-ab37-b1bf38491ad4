import importlib
import os

class ModuleChecker:
    def __init__(self, requirements_file=r'Resources\requirement.txt'):
        self.requirements_file = requirements_file
        self.name_to_import_map = {}  # Will be built from the requirements file
        self.missing_modules = []

    def read_requirements(self):
        if not os.path.exists(self.requirements_file):
            raise FileNotFoundError(f"Requirements file '{self.requirements_file}' not found.")
        
        requirements = []
        with open(self.requirements_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    module = line.split('==')[0].strip()
                    if module:
                        requirements.append(module)
        return requirements

    def build_import_map(self, requirements):
        # Map package names to import names (some need translation)
        self.name_to_import_map = {
            pkg: self.get_import_name(pkg) for pkg in requirements
        }

    def get_import_name(self, pkg_name):
        # Known exceptions and common mismatches
        known_aliases = {
            "PyJWT": "jwt",
            "pymupdf": "fitz",
            "reportlab": "reportlab",
            "pywin32-ctypes": "pywin32ctypes",
            "pyinstaller-hooks-contrib": "PyInstallerHooks",
            "altgraph": "altgraph",
            "pycparser": "pycparser",
            "pyinstaller": "PyInstaller",
            "python-dateutil": "dateutil",
            "typing_extensions": "typing_extensions",
            "openpyxl": "openpyxl",
            "et_xmlfile": "et_xmlfile",
            "speedtest-cli": "speedtest",
            "psutil": "psutil",
            "httpx": "httpx",
            "httpcore": "httpcore",
        }
        return known_aliases.get(pkg_name, pkg_name.replace("-", "_"))

    def check_modules(self):
        requirements = self.read_requirements()
        self.build_import_map(requirements)

        for pkg_name, import_name in self.name_to_import_map.items():
            try:
                importlib.import_module(import_name)
            except ImportError:
                self.missing_modules.append(pkg_name)

    def report(self):
        if not self.missing_modules:
            print("✅ All required modules are installed.")
            return

        print("\n❌ The following modules are missing:\n")
        for mod in self.missing_modules:
            print(f"  - {mod}")
        
        pip_command = f"pip install {' '.join(sorted(set(self.missing_modules)))}"
        print("\n📦 Copy and paste this command to install them:\n")
        print(f"    {pip_command}\n")
        print("⚠️  If any of these are custom modules, please add them to your project manually.")

    def run(self):
        self.check_modules()
        self.report()

if __name__ == "__main__":
    ModuleChecker().run()