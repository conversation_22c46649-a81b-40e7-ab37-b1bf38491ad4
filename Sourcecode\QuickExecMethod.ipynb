{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Purpose - To Quickly Execute Code, such as Import XML file into our tally, etc\n", "### Created By - <PERSON><PERSON><PERSON>\n", "### Created Date - 29-01-2025\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import requests\n", "import traceback"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Method"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def ImportXML(strRequestXMLPath, strResponseXMLPath=\"response.xml\", url= \"http://************:10050/\"):\n", "    try:\n", "        # Read XML content from file\n", "        with open(strRequestXMLPath, \"r\", encoding=\"utf-8\") as file:\n", "            xml_data = file.read()\n", "\n", "        # Define headers\n", "        headers = {\n", "            \"Content-Type\": \"text/xml\",\n", "            \"Cache-Control\": \"no-cache\"\n", "        }\n", "\n", "        # Send POST request\n", "        response = requests.post(url, headers=headers, data=xml_data)\n", "\n", "        # Save the response to an XML file\n", "        with open(strResponseXMLPath, \"w\", encoding=\"utf-8\") as file:\n", "            file.write(response.text)\n", "\n", "        print( f\"Response saved to {strResponseXMLPath}\")\n", "\n", "    except Exception as <PERSON><PERSON><PERSON><PERSON>:\n", "        print(traceback.print_exc())\n", "        print( f\"Failed to Export the data,  error: {str(GenError)}\")\n", "\n", "def ImportXMLInTallyUsingDir(strDirRequestXML, url=\"http://************:10050/\"):\n", "    \"\"\"\n", "    Imports XML files from a given directory into Tally using the specified URL.\n", "    \n", "    Parameters:\n", "        strDirRequestXML (str): The directory containing request XML files.\n", "        url (str): The Tally server URL (default: \"http://************:10050/\").\n", "    \n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    try:\n", "        # Check if the directory exists\n", "        if not os.path.exists(strDirRequestXML):\n", "            raise FileNotFoundError(f\"Directory '{strDirRequestXML}' does not exist.\")\n", "\n", "        # Iterate through files in the directory\n", "        for filename in os.listdir(strDirRequestXML):\n", "            if filename.lower().endswith(\".xml\"):  # Check for XML files\n", "                strRequestXMLPath = os.path.join(strDirRequestXML, filename)  # Absolute path\n", "                \n", "                # Define response XML file path\n", "                response_dir = os.path.join(strDirRequestXML, \"response\")\n", "                os.makedirs(response_dir, exist_ok=True)  # Create 'response' directory if it doesn't exist\n", "                strResponseXMLPath = os.path.join(response_dir, f\"response-{filename}\")\n", "                open(strResponseXMLPath, 'w').close()\n", "                \n", "                # Call ImportXML method\n", "                try:\n", "                    ImportXML(strRequestXMLPath = strRequestXMLPath, strResponseXMLPath=strResponseXMLPath, url=url)\n", "                    print(f\"Successfully imported: {strRequestXMLPath}\")\n", "                except Exception as e:\n", "                    print(f\"Error importing {strRequestXMLPath}: {e}\")\n", "\n", "    except Exception as e:\n", "        print(f\"Error in ImportXMLInTallyUsingDir: {e}\")\n", "\n", "def send_xml_to_tally(xml_file_path, url=\"http://192.168.1.21:9000/\", response_file=\"response.xml\"):\n", "    \"\"\"\n", "    Reads an XML file and sends it to the specified Tally server URL.\n", "    Saves the server response to an XML file.\n", "\n", "    Args:\n", "    xml_file_path (str): The path to the XML file to send.\n", "    url (str): The Tally server URL.\n", "    response_file (str): The path where the response XML will be saved.\n", "\n", "    Returns:\n", "    str: Confirmation message or error details.\n", "    \"\"\"\n", "    try:\n", "        # Read XML content from file\n", "        with open(xml_file_path, \"r\", encoding=\"utf-8\") as file:\n", "            xml_data = file.read()\n", "\n", "        # Define headers\n", "        headers = {\n", "            \"Content-Type\": \"text/xml\",\n", "            \"Cache-Control\": \"no-cache\"\n", "        }\n", "\n", "        # Send POST request\n", "        response = requests.post(url, headers=headers, data=xml_data)\n", "\n", "        # Save the response to an XML file\n", "        with open(response_file, \"w\", encoding=\"utf-8\") as file:\n", "            file.write(response.text)\n", "\n", "        return f\"Response saved to {response_file}\"\n", "\n", "    except FileNotFoundError:\n", "        return \"Error: The specified file was not found.\"\n", "    except requests.exceptions.RequestException as e:\n", "        return f\"Error: Unable to send data to the server. {str(e)}\"\n", "    except IOError as e:\n", "        return f\"Error: Unable to save the response file. {str(e)}\""]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 1030_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 1030_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 1031_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 1031_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7018_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7018_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7093_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7093_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7119_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7119_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7125_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7125_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7128_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7128_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7393_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7393_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7394_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7394_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7395_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7395_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7515_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7515_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7579_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7579_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7606_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7606_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7609_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7609_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7620_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7620_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7622_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7622_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD 7624_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD 7624_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD DD1089_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD DD1089_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD DD1090_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD DD1090_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD DD1091_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD DD1091_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD DD1094_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD DD1094_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD DD1095_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD DD1095_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD DD1097_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD DD1097_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD DD1098_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD DD1098_gptResponse.xml\n", "Response saved to H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\response\\response-PICD7586_gptResponse.xml\n", "Successfully imported: H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\PICD7586_gptResponse.xml\n"]}], "source": ["ImportXMLInTallyUsingDir(strDirRequestXML = r\"H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\", url=\"http://************:10050/\")\n", "# ImportXML(strRequestXMLPath=r\"H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\New folder\\PICD 1030_gptResponse.xml\", strResponseXMLPath=r\"H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\New folder\\response\\response-PICD 1030_gptResponse.xml\", url= \"http://************:10050/\")\n", "\n", "# send_xml_to_tally(xml_file_path=r\"H:\\DEVELOPER_PUBLIC\\Developers\\Devanshi\\Customers\\REAL\\AccuVelocity\\TallyExportImport\\JournalICD\\ICDJournalEntry-Req.xml\", url=\"http://************:10050/\", response_file=r\"H:\\AI Data\\18_FairdealInternational\\TallyXML\\All Invoices XML\\New folder\\response\\response-PICD 1030_gptResponse.xml\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}