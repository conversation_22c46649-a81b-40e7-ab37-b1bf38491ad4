@echo off

:: Set the paths based on the project structure
SET SCRIPT_RELATIVE_PATH=sourcecode
SET OUTPUT_RELATIVE_PATH=exe\LicenseGenerator

:: Set the name of the Python script and the desired exe name
SET SCRIPT_NAME=LicenseGenerator.py
SET EXE_NAME=LicenseGen.exe

:: Create the output directory if it doesn't exist
IF NOT EXIST "%OUTPUT_RELATIVE_PATH%" (
    mkdir "%OUTPUT_RELATIVE_PATH%"
)

:: Change to the script's directory
cd /d "%SCRIPT_RELATIVE_PATH%"

:: Run PyInstaller to create the executable with the specified output path
pyinstaller --onefile --distpath "..\%OUTPUT_RELATIVE_PATH%" --name "%EXE_NAME%" %SCRIPT_NAME%

:: Pause to keep the window open and show the result
pause
