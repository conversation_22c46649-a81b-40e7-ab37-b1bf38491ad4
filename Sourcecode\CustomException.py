
class InvalidFilePathError(Exception):
    def __init__(self, strFilePath, strErrorMessage="Provided file path is invalid, Location:"):
        super().__init__(f"{strErrorMessage} [{strFilePath}] ")

class NoFilesProvidedError(Exception):
    def __init__(self, strErrorMessage="No Files Provided, Please provide files for processing."):
        super().__init__(f"{strErrorMessage}")

class UnsupportedFileFormatError(Exception):
    """Raised when a file's content type is not supported."""
    def __init__(self, file_path, content_type):
        self.message = f"Unsupported file format: [{file_path}] with content type '{content_type}'"
        super().__init__(self.message)