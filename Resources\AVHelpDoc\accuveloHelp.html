<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AccuVelocity Help Page</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            box-sizing: border-box;
            text-align: center;
            background-color: #ffffff;
        }

        *{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

         .sidebar {
            width: 350px;
            background-color: #002147;
            color: white;
            padding: 20px;
            position: fixed;
            height: 100vh;
            top: 0;
            left: 0;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .sidebar h1 {
            display: flex;
            align-items: center;
            font-size: 28px;
            margin: 0;
            color: #ffffff;
            padding-bottom: 10px;
            margin-left: 25px;
            margin-top: 10px;
            /* border-bottom: 2px solid #444; */
            margin-bottom: 20px;
        }

        .sidebar .menu-item {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: white;
            padding: 12px;
            margin-top: 10px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }

        .sidebar .menu-item i {
            font-size: 20px;
            margin-right: 15px;
        }

        .sidebar .menu-item:hover {
            background-color: #0078d7;
        }

        .social-icons {
            margin-top: auto;
            display: flex;
            justify-content: space-between;
            padding-top: 20px;
            flex-direction:column;
            display: flex;
         
        }

        .social-icons h3 {
            color: white;
            margin-bottom: 10px;
            line-height: 20px;
        }

        .social-icons a {
            text-decoration: none;
            margin-right: 10px;
            color: white;
        }

        .social-icons a i {
            background: linear-gradient(135deg, #007bff, #00d4ff);
            color: white;
            font-size: 20px;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .social-icons a i:hover {
            background: linear-gradient(135deg, #0056b3, #0099cc);
            transform: scale(1.3);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }
        

        .main-header {
            position: fixed;
            top: 0;
            left: 350px;
            width: calc(100% - 350px);
            height: 100px;
            background: linear-gradient(135deg, #21c6e7, #007bff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 25px;
            font-size: 18px;
            font-weight: bold;
            z-index: 1000;
        }

        .main {
            margin-left: 350px;
            margin-top: 100px;
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto; /* Only this part scrolls */
            /* background-color: #ffffff; */
            height: calc(100vh - 100px); /* Full height minus header */
            box-sizing: border-box;
            background-color:none;
        }


        .main-header-content {
            margin-top: 10px;
            padding: 0;
            text-align: left;
        }

        .section {
            /* background: white; */
            padding: 20px;
            margin-bottom: 20px;
            text-align: left;
            /* box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); */
            /* box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); */
        }

        .section h2 {
        color: #002147;
        text-align: left;
        margin-bottom: 10px;
        margin-top: 10px;
        font-size: 24px;
        line-height: 30px;
        }
        .section #purchasewithoutinv #heading{
            margin-bottom: 10px;
            margin-top: 10px;
        }

        .section p {
            line-height: 1.6;
            text-align: left;
            font-size: 16px;
            color: #555;
        }
        .section ol {
            margin-left: 20px;
            padding-left: 20px;
            line-height: 1.8;
        }

        .section h3 {
            line-height: 30px;

        }

        .section 

        iframe {
            width: 40%;
            height: 315px;
            border: none;
            margin: 10px 0;
            display: block;
            margin-left: auto;
            margin-right: auto;
            padding: 20px;
        }

        img {
            width: 60%;
            max-width: 600px;
            height: auto;
            margin: 20px auto;
            display: block;
        }

        a {
            color: #0078d7;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        a:hover {
            color: #0056b3;
            text-decoration: underline;
        }

        .sign-in-button {
            background: linear-gradient(135deg, #007bff, #00d4ff);
            color: white;
            font-size: 18px;
            font-weight: bold;
            padding: 12px 30px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .sign-in-button:hover {
            background: linear-gradient(135deg, #0056b3, #0099cc);
            transform: scale(1.05);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            justify-content: center;
            align-items: center;
        }

        .modal img {
            max-width: 80%;
            max-height: 80%;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 30px;
            font-size: 30px;
            color: white;
            cursor: pointer;
        }
    </style>
</head>
<body>

<!-- Sidebar -->
<div class="sidebar">
    <h1 class="accuvelocityicon">
        <a href="https://www.accuvelocity.in">AccuVelocity</a>
    </h1>
    <a href="#introduction" class="menu-item active"><i class="fas fa-th-large"></i> Introduction</a>
    <div class="sidebarsunmenu">
        <a href="#process-documents" class="menu-item" onclick="toggleSubmenu('submenu1')"><i
                class="fas fa-file-alt"></i> How to Process Documents</a>
        <div id="submenu1" class="submenu">
            <a href="#purchasewithinv" class="menu-item">1.Purchase With Inventory Voucher</a>
            <a href="#purchasewithoutinv" class="menu-item">2.Purchase Without Inventory Voucher</a>
            <a href="#salesdelivery" class="menu-item"> 3.Sales Delivery Note Voucher </a>
            <a href="#journalguid" class="menu-item">4.Journal Entry Voucher</a>
            <a href="#bankStatements" class="menu-item">5.Bank Statements</a>
            <a href="#receiptnote" class="menu-item">6.Receipt Note</a>
            <a href="#purchaseorder" class="menu-item">7.Purchase Order</a>
        </div>
    </div>

    <a href="#faqs" class="menu-item"><i class="fas fa-question-circle"></i> FAQs</a>
    <a href="#troubleshoot" class="menu-item"><i class="fas fa-tools"></i> Troubleshoot</a>
    <a href="#support" class="menu-item"><i class="fas fa-headset"></i> Support</a>
    <a href="#conclusion" class="menu-item"><i class="fas fa-check-circle"></i> Conclusion</a>

    <div class="social-icons">
        <h3>Connect with us:</h3>
        <div>
            <a href="https://www.facebook.com/people/AccuVelocity/**************/" target="_blank"><i
                    class="fab fa-facebook"></i></a>
            <a href="https://www.youtube.com/@AccuVelocity" target="_blank"><i class="fab fa-youtube"></i></a>
            <a href="https://www.instagram.com/accu_velocity/" target="_blank"><i class="fab fa-instagram"></i></a>
            <a href="https://www.linkedin.com/company/accuvelocity?trk=public_post_follow-view-profile"
                target="_blank"><i class="fab fa-linkedin"></i></a>
        </div>
    </div>
</div>

<!-- header -->
<div class="main-header">
    <div class="main-header-content">
        <h2>How can we help?</h2>
        <p class="font-text">Find guides, solutions, and support to make the most of our services</p>
    </div>
    <a href="https://www.accuvelocity.in" class="sign-in-button">Sign In</a>             
</div>

<!-- Main Content -->
<div class="main">


    <!-- Introduction Section -->
    <div id="introduction" class="section">
        <h2>1. Introduction</h2>
        <p>Welcome to the AccuVelocity Help Document, a comprehensive guide to mastering the software's
            functionalities and features. This document offers detailed instructions, tips, and troubleshooting
            advice to optimize your workflow with AccuVelocity, a state-of-the-art document processing tool. It
            includes step-by-step installation instructions, extensive support information, and FAQs to assist with
            any issues.</p>
        <iframe src="https://www.youtube.com/embed/6Ympjdx1FEo"  title="Getting Started Tutorial"  allowfullscreen></iframe>    
        <p><a href="https://youtu.be/6Ympjdx1FEo" target="_blank">Watch on YouTube</a></p>
    </div>

    <!-- Process Documents Section -->
    <div id="process-documents" class="section">
        <h2> How to Process Documents</h2>



               <div id="purchasewithinv" class="section">


           <div style="display: flex; align-items: center; gap: 5px;"> <h2>User Guid For Purchase With Inventory:</h2>
            <p><a href="https://youtu.be/VhYlbRJ3V-Y" target="_blank">(Watch on YouTube)</a></p></div>

            <h3>Step-1 AccuVelocity AI:</h3>
            <p>When you open Tally, you will see the AccuVelocity AI button on your Gateway of Tally.</p>
            <p>To start using AccuVelocity Software <span style="font-weight: 600;">click on the AccuVelocity AI button</span> . </p>
            <img src="assets\Purchase with Inventory\1. Gateway of Tally.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-2 Voucher Processing:</h3>
            <p>This will provide you with the following options:</p>
            <p>
                <ol>
                    <li>AccuVelocity Voucher Processing</li>
                    <li>AccuVelocity Activity Log</li>
                    <li>AccuVelocity Help Page</li>
                    <li>Quit</li>
                </ol>
            </p>
            <p>To begin processing your documents,<span style="font-weight: 600;">click on AccuVelocity Voucher Processing</span> .</p>
            <img src="assets\Purchase with Inventory\2. AccuVelocityAI Features.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-3 Select Voucher Type:</h3>
            <p><span style="font-weight: 600;">Choose Purchase with Inventory </span>from the available voucher types.
            <p>
            <!-- <p>This is used to process documents related to goods being delivered to customers
            <p> -->
                <img src="assets\Purchase with Inventory\4. Accuvelocity Voucher Processing - Purchase with Inventory.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-4 Select Document for Processing:</h3>
            <p>To process a <span style="font-weight: 600;">single purchase document</span> select the file from your system or specify its location.</p>
            <p>To process<span style="font-weight: 600;"> multiple purchase documents</span> select the ZIP file path containing all documents.</p>
            <p>
                <span style="font-weight: 600; font-style: italic ;">
                    Note:
                    To provide multiple documents for processing, create a folder and add all the documents to this
                    folder.
                    Then, create a zip archive of this folder and provide the path to this zip file.
                  </span>
            </p>


            <img src="assets\Purchase with Inventory\4. Accuvelocity Voucher Processing - Purchase with Inventory.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-5 Verify File Selection & Backup</h3>
            <p>Confirm the document path you selected.</p>
            <p>It is recommended to keep the<span style="font-weight: 600;"> "Backup Before Import" </span>option set to YES to ensure a safety backup
                before inserting new vouchers.</p>
            <img src="assets\Purchase with Inventory\5. Purchase With Inventory - Verify File Path And Backup Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-6 Start Processing:</h3>
            <p><span style="font-weight: 600;">Click Process Voucher</span>(top-right of Tally) or Accept <span style="font-weight: 600;">Yes or Ctrl + v</span> to start processing.</p>
            <img src="assets\Purchase with Inventory\6. Purchase With Inventory - Start Processing Document Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-7 Processing Confirmation</h3>
            <p><span style="font-weight: 600;">A prompt window will appear</span>, confirming that the document is being processed.</p>
            <p>Please wait for the processed report to be generated.</p>
            <img src="assets\Purchase with Inventory\7. Purchase With Inventory - Processing Confirmation Window.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-8 Verify Processed Report:</h3>
            <p>Once processing is complete, the AccuVelocity Processed Report will display the following details:
            <p>• Filename</p>
            <p>• Received Date</p>
            <p>• Invoice Number</p>
            <p>• Invoice Date</p>
            <p>• Total Amount</p>
            <p>• Vendor Name</p>
            <p>• Tally Status</p>
            <p>• Estimated Time Saved</p>
            <p>•AccuVelocity Comments</p>
            </p>
           
            <img src="assets\Purchase with Inventory\8. Purchase With Inventory Entry - Processed Document Report.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">
            <p>To view the location of the Processed Document Report, <span style="font-weight: 600;">click the Open Report Directory button</span></p>
            <img src="assets\Purchase with Inventory\8.1 Purchase with Inventory Entry - To view the location of the Processed Document Report, click the Open Report Directory button.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">
            

            <p><span style="font-weight: 600; margin-bottom: 10px;">Understanding Tally Status:</span></p>    
            <p>The Tally Status column in the processed report can have three values:
            <p> <span style="font-weight: 600; color: rgb(27, 95, 27);">• Success </span> - The document was processed and entered into Tally
                without any errors.</p>
            <p><span style="font-weight: 600;  color: brown;  ">• Partial Success</span> - The document was processed, but some validation issues were found. </p>    
            <p style="margin-left: 10px; ">Possible reasons: </p>
            <p style="margin-left: 20px;" > - Item not found in inventory</p>
            <p style="margin-left: 20px;" > - Duplicate entry detected</p>
            <p style="margin-left: 20px;" > - Other validation errors (Check the AccuVelocity Comments for details)</p>
            <p> <span style="font-weight: 600;  color: rgb(233, 10, 10); ">• Skipped</span> - The document was not processed due to an error.
                Check the AccuVelocity Comments column for more details.</p>
            </p>

            <h3>Step-9 Import Processed Document:</h3>

            <p><span style="font-weight: 600;">1.Process of importing Processed Document:</span></p> 
            <p>You can start the process in two ways:
                You can press the shortcut key TTP
                OR
                If you cannot use the shortcut TTP then: </p>
            <p> Select Tally Post API</p>
            <img src="assets/pwi_1.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">
            <p>Select API Templates</p>
            <img src="assets/pwi_2.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">
            <p>Select Purchase with Inventory</p>
            <img src="assets/pwi_3.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">
            <p><span style="font-weight: 600;">2.Importing Documents:</span></p> 
            <p>You will be redirected to the Home Page of Tally  AND   Right side of the interface. You will find the <span style="font-weight: 600;">"Finish  Import"</span>
                option there. Select this to complete the import process efficiently.</p>
            <img src="assets/importd_1.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">
            <p><span style="font-weight: 600;">3.Tally Window:</span></p>     
            <p>You will see the Tally window command prompt on your screen.
                import is successful a message will appear saying <span style="font-weight: 600;"> "Request was successfully"</span> Press any key to return
                to the main interface of Tally.</p>
            <img src="assets\cmd Request Successful.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">


            <h3>Step-10 Reviewing & Moving Processed Entries in Tally:</h3> 
            <p><span style="font-weight: 600;">Once AccuVelocity processes your voucher, it is stored in AV Purchase A/C Ledger To review and move
                it to the correct voucher type, follow these steps:</span></p>    

            <p>1.Navigate to Gateway of Tally, <span style="font-weight: 600;">open Daybook </span>and <span style="font-weight: 600;">filter by the date </span>of the processed entry.</p>
            <img src="assets\DayBook\1. Purchase with inventory - AV Tax Journal Voucher type Date Filter.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <p>2.Apply the AV Prefix Voucher Type filter such as<span style="font-weight: 600;"> AV Purchase A/C</span>, and select a processed document
                voucher with the latest unique voucher number to view the detailed entry.</p>
            <!-- <img src="assets/tally.png" alt=""> -->

            <p>3.<span style="font-weight: 600;">Click Other Voucher</span>(located on the right-middle of Tally) or <span style="font-weight: 600;"> press F10 </span>to change the voucher type.
            </p>
            <img src="assets\DayBook\2. Purchase with Inventory - Change Voucher Type.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <p>4.Press<span style="font-weight: 600;"> Ctrl + A to save </span> and successfully transfer the entry to the selected voucher type.</p>
            <!-- <img src="assets/tally.png" alt=""> -->

            <span style="font-weight: 600;;">
                <p>• All done AccuVelocity has successfully processed your Purchase with Inventory.</p>
                <p>• For further assistance, refer to the AccuVelocity Help Page</p>
            </span>
            

        </div>

        <div id="purchasewithoutinv">

           
            
            <div style="display: flex; align-items: center; gap: 5px;"> <h2>User Guid For Purchase Without Inventory:</h2>
                <p><a href="https://www.youtube.com/watch?v=EDPUzG1dQME" target="_blank">(Watch on YouTube)</a></p></div>

            <h3>Step-1 AccuVelocity AI:</h3>
            <p>When you open Tally, you will see the AccuVelocity AI button on your Gateway of Tally.</p>
            <p>To start using AccuVelocity Software <span style="font-weight: 600;">click on the AccuVelocity AI button</span>. </p>
            <img src="assets\Purchase Without Inventory\1. Gateway of Tally.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-2 Voucher Processing:</h3>
            <p>This will provide you with the following options:</p>
            <p>
                <ol>
                    <li>AccuVelocity Voucher Processing</li>
                    <li>AccuVelocity Activity Log</li>
                    <li>AccuVelocity Help Page</li>
                    <li>Quit</li>
                </ol>
            </p>
            <p>To begin processing your documents,<span style="font-weight: 600;">click on AccuVelocity Voucher Processing</span> .</p>
            <img src="assets\Purchase Without Inventory\2. AccuVelocityAI Features.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >

            <h3>Step-3 Select Voucher Type:</h3>
            <p><span style="font-weight: 600;">Choose Purchase without Inventory </span>from the available voucher types.
            <p>
            <!-- <p>This is used to process documents related to goods being delivered to customers
            <p> -->
                <img src="assets\Purchase Without Inventory\4. Accuvelocity Voucher Processing - Purchase without Inventory.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-4 Select Document for Processing:</h3>
            <p>To process a <span style="font-weight: 600;">single purchase document</span> select the file from your system or specify its location.</p>
            <p>To process<span style="font-weight: 600;"> multiple purchase documents</span> select the ZIP file path containing all documents.</p>
            <p>
                <span style="font-weight: 600; font-style: italic ;">
                    Note:
                    To provide multiple documents for processing, create a folder and add all the documents to this
                    folder.
                    Then, create a zip archive of this folder and provide the path to this zip file.
                   </span>
            </p>


            <img src="assets\Purchase Without Inventory\4. Accuvelocity Voucher Processing - Purchase without Inventory.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-5 Verify File Selection & Backup</h3>
            <p>Confirm the document path you selected.</p>
            <p>It is recommended to keep the<span style="font-weight: 600;"> "Backup Before Import" </span>option set to YES to ensure a safety backup
                before inserting new vouchers.</p>
                <img src="assets\Purchase Without Inventory\5. Purchase Without Inventory- Verify File Path And Backup Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-6 Start Processing:</h3>
            <p><span style="font-weight: 600;">Click Process Voucher</span>(top-right of Tally) or Accept <span style="font-weight: 600;">Yes or Ctrl + v</span> to start processing.</p>
            <img src="assets\Purchase Without Inventory\6. Purchase Without Inventory - Start Processing Document Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-7 Processing Confirmation</h3>
            <p>A prompt window will appear, confirming that the document is being processed.</p>
            <p>Please wait for the processed report to be generated.</p>
            <img src="assets\Purchase Without Inventory\7. Purchase Without Inventory - Processing Confirmation Window.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-8 Verify Processed Report:</h3>
            <p>Once processing is complete, the AccuVelocity Processed Report will display the following details:
                <p>• Filename</p>
                <p>• Received Date</p>
                <p>• Invoice Number</p>
                <p>• Invoice Date</p>
                <p>• Total Amount</p>
                <p>• Vendor Name</p>
                <p>• Tally Status</p>
                <p>• Estimated Time Saved</p>
                <p>•AccuVelocity Comments</p>
            </p>
            <img src="assets\Purchase Without Inventory\8. Purchase Without Inventory Entry - Processed Document Report.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">
            <p>To view the location of the Processed Document Report,<span style="font-weight: 600;">click the Open Report Directory button</span></p>
            <img src="assets\Purchase Without Inventory\8.1 Purchase Without Inventory Entry - To view the location of the Processed Document Report, click the Open Report Directory button.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">
            

            p><span style="font-weight: 600; margin-bottom: 10px;">Understanding Tally Status:</span></p>    
            <p>The Tally Status column in the processed report can have three values:
            <p> <span style="font-weight: 600; color: rgb(27, 95, 27);">• Success </span> - The document was processed and entered into Tally
                without any errors.</p>
            <p><span style="font-weight: 600;  color: brown;  ">• Partial Success</span> - The document was processed, but some validation issues were found. </p>    
            <p style="margin-left: 10px; ">Possible reasons: </p>
            <p style="margin-left: 20px;" > - Item not found in inventory</p>
            <p style="margin-left: 20px;" > - Duplicate entry detected</p>
            <p style="margin-left: 20px;" > - Other validation errors (Check the AccuVelocity Comments for details)</p>
            <p> <span style="font-weight: 600;  color: rgb(233, 10, 10); ">• Skipped</span> - The document was not processed due to an error.
                Check the AccuVelocity Comments column for more details.</p>
            </p>

            

            <h3>Step-9 Reviewing & Moving Processed Entries in Tally:</h3>
            
            <p><span style="font-weight: 600;">Once AccuVelocity processes your voucher, it is stored in AV Purchase A/C Ledger To review and move
                it to the correct voucher type, follow these steps:</span></p>    

                <p>1.Navigate to Gateway of Tally, <span style="font-weight: 600;">open Daybook </span>and <span style="font-weight: 600;">filter by the date </span>of the processed entry.</p>
                <img src="assets\DayBook\1. Purchase Without inventory - AV Tax Journal Voucher type Date Filter.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

                <p>2.Apply the AV Prefix Voucher Type filter such as<span style="font-weight: 600;"> AV Purchase A/C</span>, and select a processed document
                    voucher with the latest unique voucher number to view the detailed entry.</p>
            <!-- <img src="assets/tally.png" alt=""> -->

            <p>3.<span style="font-weight: 600;">Click Other Voucher</span>(located on the right-middle of Tally) or <span style="font-weight: 600;"> press F10 </span>to change the voucher type.
            </p>
            <img src="assets\DayBook\2. Purchase with Inventory - Change Voucher Type.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <p>4.Press<span style="font-weight: 600;"> Ctrl + A to save </span> and successfully transfer the entry to the selected voucher type.</p>
            <!-- <img src="assets/tally.png" alt=""> -->

            <span style="font-weight: 600;">
            <p>• All done AccuVelocity has successfully processed your Purchase without Inventory.</p>
            <p>• For further assistance, refer to the AccuVelocity Help Page</p>
            </span>



        </div>

        <div id="salesdelivery" class="section">


            <h2> User Guid For Delivery Note:</h2>

            <h3>Step-1 AccuVelocity AI:</h3>
            <p>When you open Tally, you will see the AccuVelocity AI button on your Gateway of Tally.</p>
            <p>To start using AccuVelocity Software <span style="font-weight: 600;">click on the AccuVelocity AI button</span>. </p>
            <img src="assets\Delivery Note\1. Gateway of Tally.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-2  Voucher Processing:</h3>
            <p>This will provide you with the following options:</p>
            <p>
            <ol>
                <li>AccuVelocity Voucher Processing</li>
                <li>AccuVelocity Activity Log</li>
                <li>AccuVelocity Help Page</li>
                <li>Quit</li>
            </ol>

            </p>
            <p>To begin processing your documents,<span style="font-weight: 600;">click on AccuVelocity Voucher Processing</span> .</p>
            <img src="assets\Delivery Note\2. AccuVelocityAI Features.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-3 Select Voucher Type:</h3>
            <p><span style="font-weight: 600;">Choose Delivery Note </span>from the available voucher types
            <p>
            <!-- <p>This is used to process documents related to goods being delivered to customers
            <p> -->
                <img src="assets\Delivery Note\4. Accuvelocity Voucher Processing - Delivery Note.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >

            <h3>Step-4 Select Document for Processing:</h3>
            <p>To process a <span style="font-weight: 600;">single Delivery document</span> select the file from your system or specify its location.</p>
            <p>To process<span style="font-weight: 600;"> multiple Delivery documents</span> select the ZIP file path containing all documents.</p>
            <p>
                <span style="font-weight: 600; font-style: italic ;">
                    Note:
                    To provide multiple documents for processing, create a folder and add all the documents to this
                    folder.
                    Then, create a zip archive of this folder and provide the path to this zip file.
                    </span>
            </p>


            <img src="assets\Delivery Note\4. Accuvelocity Voucher Processing - Delivery Note.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >

            <h3>Step-5 Verify File Selection & Backup</h3>
            <p>Confirm the document path you selected.</p>
            <p>It is recommended to keep the<span style="font-weight: 600;"> "Backup Before Import" </span>option set to YES to ensure a safety backup
                before inserting new vouchers.</p>
                <img src="assets\Delivery Note\5. Delivery Note - Verify File Path And Backup.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >

            <h3>Step-6 Start Processing:</h3>
            <p><span style="font-weight: 600;">Click Process Voucher</span>(top-right of Tally) or Accept <span style="font-weight: 600;">Yes or Ctrl + v</span> to start processing.</p>
            <img src="assets\Delivery Note\6. Delivery Note - Start Processing Document Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-7 Processing Confirmation</h3>
            <p><span style="font-weight: 600;">A prompt window will appear</span>, confirming that the document is being processed.</p>
            <p>Please wait for the processed report to be generated.</p>
            <img src="assets\Delivery Note\7. Delivery Note - Processing Confirmation Window.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >

            <h3>Step-8 Verify Processed Report:</h3>
            <p>Once processing is complete, the AccuVelocity Processed Report will display the following details:
            <p>• Filename,</p>
            <p>• Received Date,</p>
            <p>• Quotation Number,</p>
            <p>• Quotation Date,</p>
            <p>• Customer Name,</p> 
            <p>• Tally Status,</p>
            <p>• AccuVelocity Comments</p>
            <p>• Estimated Time Saved</p>
            </p>

            <img src="assets\Delivery Note\8. Delivery Note Entry - Processed Document Report.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">
            <p>To view the location of the Processed Document Report, click the Open Report Directory button</p>
            <img src="assets\Delivery Note\8.1 Delivery Note Entry - To view the location of the Processed Document Report, click the Open Report Directory button.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">


            <p><span style="font-weight: 600; margin-bottom: 10px;">Understanding Tally Status:</span></p>    
            <p>The Tally Status column in the processed report can have three values:
            <p> <span style="font-weight: 600; color: rgb(27, 95, 27);">• Success </span> - The document was processed and entered into Tally
                without any errors.</p>
            <p><span style="font-weight: 600;  color: brown;  ">• Partial Success</span> - The document was processed, but some validation issues were found. </p>    
            <p style="margin-left: 10px; ">Possible reasons: </p>
            <p style="margin-left: 20px;" > - Item not found in inventory</p>
            <p style="margin-left: 20px;" > - Duplicate entry detected</p>
            <p style="margin-left: 20px;" > - Other validation errors (Check the AccuVelocity Comments for details)</p>
            <p> <span style="font-weight: 600;  color: rgb(233, 10, 10); ">• Skipped</span> - The document was not processed due to an error.
                Check the AccuVelocity Comments column for more details.</p>
            </p>


            <h3>Step-9 Reviewing & Moving Processed Entries in Tally:</h3>
            <p><span style="font-weight: 600;">Once AccuVelocity processes your voucher, it is stored in AV Delivery Note Ledger To review and move
                it to the correct voucher type, follow these steps:</span></p>

                <p>1.Navigate to Gateway of Tally, <span style="font-weight: 600;">open Daybook </span>and <span style="font-weight: 600;">filter by the date </span>of the processed entry.</p>
                <img src="assets\DayBook\1. Delivery NOTE - AV Tax Journal Voucher type Date Filter.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

                <p>2.Apply the AV Prefix Voucher Type filter such as <span style="font-weight: 600;"> AV Delivery Note</span> , and select a processed document
                    voucher with the latest unique voucher number to view the detailed entry.</p>
                <!-- <img src="assets/tally.png" alt=""> -->

                <p>3.<span style="font-weight: 600;">Click Other Voucher</span>(located on the right-middle of Tally) or <span style="font-weight: 600;"> press F10 </span>to change the voucher type.
                </p>
                <img src="assets\DayBook\2. Delivery NOTE - Change Voucher Type.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

                <p>4.Press<span style="font-weight: 600;"> Ctrl + A to save </span> and successfully transfer the entry to the selected voucher type.</p>
                <!-- <img src="assets/tally.png" alt=""> -->
                <span style="font-weight: 600;">
                <p>• All done AccuVelocity has successfully processed your Delivery Note.</p>
                <p>• For further assistance, refer to the AccuVelocity Help Page</p>
                </span>

        </div>

        <div id="journalguid" class="section">


            <h2>User Guid For Journal Entry:</h2>

            <h3>Step-1 AccuVelocity AI:</h3>
            <p>When you open Tally, you will see the AccuVelocity AI button on your Gateway of Tally.</p>
            <p>To start using AccuVelocity Software <span style="font-weight: 600;">click on the AccuVelocity AI button</span>. </p>
            <img src="assets\Journal Entry\1. Gateway of Tally.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-2 Voucher Processing:</h3>
            <p>This will provide you with the following options:</p>
            <p>
                <ol>
                    <li>AccuVelocity Voucher Processing</li>
                    <li>AccuVelocity Activity Log</li>
                    <li>AccuVelocity Help Page</li>
                    <li>Quit</li>
                </ol>

            </p>
            <p>To begin processing your documents,<span style="font-weight: 600;">click on AccuVelocity Voucher Processing</span> .</p>
            <img src="assets\Journal Entry\2. AccuVelocityAI Features.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-3 Select Voucher Type:</h3>
            <p><span style="font-weight: 600;">Choose Journal Entry </span>from the available voucher types.
            <p>
            <!-- <p>This is used to process documents related to goods being delivered to customers
            <p> -->
                <img src="assets\Journal Entry\4. Accuvelocity Voucher Processing - Journal Entry.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-4 Select Document for Processing:</h3>
            <p>To process a <span style="font-weight: 600;">single Journal document</span> select the file from your system or specify its location.</p>
            <p>To process<span style="font-weight: 600;"> multiple Journal documents</span> select the ZIP file path containing all documents.</p>
            <p>
                <span style="font-weight: 600; font-style: italic ;">
                    Note:
                    To provide multiple documents for processing, create a folder and add all the documents to this
                    folder.
                    Then, create a zip archive of this folder and provide the path to this zip file.
                   </span>
            </p>


            <img src="assets\Journal Entry\4. Accuvelocity Voucher Processing - Journal Entry.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-5 Verify File Selection & Backup</h3>
            <p>Confirm the document path you selected.</p>
            <p>It is recommended to keep the<span style="font-weight: 600;"> "Backup Before Import" </span>option set to YES to ensure a safety backup
                before inserting new vouchers.</p>
                <img src="assets\Journal Entry\5. Journal - File Path Verify & Backup Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >

            <h3>Step-6 Start Processing:</h3>
            <p><span style="font-weight: 600;">Click Process Voucher</span>(top-right of Tally) or Accept <span style="font-weight: 600;">Yes or Ctrl + v</span> to start processing.</p>
            <img src="assets\Journal Entry\6. Journal - Start Processing Document Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-7 Processing Confirmation</h3>
            <p><span style="font-weight: 600;">A prompt window will appear</span>, confirming that the document is being processed.</p>
            <p>Please wait for the processed report to be generated.</p>
            <img src="assets\Journal Entry\7. Journal - Processing Confirmation Window.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-8 Verify Processed Report:</h3>
            <p>Once processing is complete, the AccuVelocity Processed Report will display the following details:
                <p>• Filename</p>
                <p>• Received Date</p>
                <p>• Invoice Number</p>
                <p>• Invoice Date</p>
                <p>• Total Amount</p>
                <p>• Vendor Name</p>
                <p>• Tally Status</p>
                <p>• Estimated Time Saved</p>
                <p>•AccuVelocity Comments</p>

            </p>

            <img src="assets\Journal Entry\8. Journal Entry - Processed Document Report.png" alt=""  onclick="openModal(this.src)" style="cursor: pointer;">
            <p> To view the location of the Processed Document Report, click the Open Report Directory button.png</p>
            <img src="assets\Journal Entry\8.1 Journal Entry - To view the location of the Processed Document Report, click the Open Report Directory button.png" alt=""  onclick="openModal(this.src)" style="cursor: pointer;">


            

            <p><span style="font-weight: 600; margin-bottom: 10px;">Understanding Tally Status:</span></p>    
            <p>The Tally Status column in the processed report can have three values:
            <p> <span style="font-weight: 600; color: rgb(27, 95, 27);">• Success </span> - The document was processed and entered into Tally
                without any errors.</p>
            <p><span style="font-weight: 600;  color: brown;  ">• Partial Success</span> - The document was processed, but some validation issues were found. </p>    
            <p style="margin-left: 10px; ">Possible reasons: </p>
            <p style="margin-left: 20px;" > - Item not found in inventory</p>
            <p style="margin-left: 20px;" > - Duplicate entry detected</p>
            <p style="margin-left: 20px;" > - Other validation errors (Check the AccuVelocity Comments for details)</p>
            <p> <span style="font-weight: 600;  color: rgb(233, 10, 10); ">• Skipped</span> - The document was not processed due to an error.
                Check the AccuVelocity Comments column for more details.</p>
            </p>

            <h3>Step-9 Reviewing & Moving Processed Entries in Tally:</h3>

            <p><span style="font-weight: 600;">Once AccuVelocity processes your voucher, it is stored in AV Tax Journal Ledger To review and move
                it to the correct voucher type, follow these steps:</span></p> 

                <p>1.Navigate to Gateway of Tally, <span style="font-weight: 600;">open Daybook </span>and <span style="font-weight: 600;">filter by the date </span>of the processed entry.</p>
                <img src="assets\DayBook\1. Journal - AV Tax Journal Voucher type Date Filter.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >

            <p>2.Apply the AV Prefix Voucher Type filter such as <span style="font-weight: 600;"> AV Tax Journal</span>, and select a processed document
                voucher with the latest unique voucher number to view the detailed entry.</p>
            <!-- <img src="assets/tally.png" alt=""> -->

            <p>3.<span style="font-weight: 600;">Click Other Voucher</span>(located on the right-middle of Tally) or <span style="font-weight: 600;"> press F10 </span>to change the voucher type.
            </p>
            <img src="assets\DayBook\2. Journal - Change Voucher Type.png" onclick="openModal(this.src)" style="cursor: pointer;">

            <p>4.Press<span style="font-weight: 600;"> Ctrl + A to save </span> and successfully transfer the entry to the selected voucher type.</p>
            <!-- <img src="assets/tally.png" alt=""> -->

            <span style="font-weight: 600;;">
            <p>• All done AccuVelocity has successfully processed your Journal Entry.</p>
            <p>• For further assistance, refer to the AccuVelocity Help Page</p>
            </span>
        </div>
    

        <div id="bankStatements" class="section">


            <h2>User Guid For Bank Statements:</h2>

            <h3>Step-1 AccuVelocity AI:</h3>
            <p>When you open Tally, you will see the AccuVelocity AI button on your Gateway of Tally.</p>
            <p>To start using AccuVelocity Software <span style="font-weight: 600;">click on the AccuVelocity AI button</span>. </p>
            <img src="assets/Bank Statement/1. Gateway of Tally.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-2 Voucher Processing:</h3>
            <p>This will provide you with the following options:</p>
            <p>
                <ol>
                    <li>AccuVelocity Voucher Processing</li>
                    <li>AccuVelocity Activity Log</li>
                    <li>AccuVelocity Help Page</li>
                    <li>Quit</li>
                </ol>

            </p>
            <p>To begin processing your documents,<span style="font-weight: 600;">click on AccuVelocity Voucher Processing</span> .</p>
            <img src="assets/Bank Statement/2. AccuVelocityAI Features.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-3 Select Voucher Type:</h3>
            <p><span style="font-weight: 600;">Choose Bank Statement </span>from the available voucher types.
            <p>
            <!-- <p>This is used to process documents related to goods being delivered to customers
            <p> -->
                <img src="assets/Bank Statement/4. Accuvelocity Voucher Processing - Bank Statements.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-4 Select Document for Processing:</h3>
            <p>To process a <span style="font-weight: 600;">single Bank Statement</span> select the file from your system or specify its location.</p>
            <p>To process<span style="font-weight: 600;"> multiple Bank Statement</span> select the ZIP file path containing all documents.</p>
            <p>
                <span style="font-weight: 600; font-style: italic ;">
                    Note:
                    To provide multiple Bank Statements for processing, create a folder and add all the documents to this
                    folder.
                    Then, create a zip archive of this folder and provide the path to this zip file.
                </span>
            </p>


            <img src="assets/Bank Statement/4.1 Accuvelocity Voucher Processing - Bank Statements.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-5 Verify File Selection & Backup</h3>
            <p>Confirm the document path you selected.</p>
            <p>It is recommended to keep the<span style="font-weight: 600;"> "Backup Before Import" </span>option set to YES to ensure a safety backup
                before inserting new vouchers.</p>
                <img src="assets/Bank Statement/5. Bank Statement - File Path Verify & Backup Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >

            <h3>Step-6 Start Processing:</h3>
            <p><span style="font-weight: 600;">Click Process Voucher</span>(top-right of Tally) or Accept <span style="font-weight: 600;">Yes or Ctrl + v</span> to start processing.</p>
            <img src="assets/Bank Statement/6. Bank Statement - Start Processing Accept Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-7 Processing Confirmation</h3>
            <p><span style="font-weight: 600;">A prompt window will appear</span>, confirming that the document is being processed.</p>
            <p>Please wait for the processed report to be generated.</p>
            <img src="assets/Bank Statement/7. BankStatment - Confirmation prompt.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-8 Verify Processed Report:</h3>
            <p>Once processing is complete, the AccuVelocity Processed Report will display the following details:
                <p>• CustomerName</p>
                <p>• Customer Bank Account Name</p>
                <p>• Time Saved Today (Minutes)</p>
                <p>• Bank Statement File Name</p>
                <p>• Start Execution Time</p>
                <p>• End Execution Time</p>
                <p>• Total No. of Transactions Found</p>
                <p>• Total No. of Predicted Transactions</p>
                <p>• Total No. of Suspense Transactions</p>
                <p>• Total No. of Duplicated Transactions</p>
                <p>•  XML Status</p>
                <p>• AccuVelocity Comments</p>
            </p>

            <img src="assets/Bank Statement/8. BankStatement with Inventory - Report.png" alt=""  onclick="openModal(this.src)" style="cursor: pointer;">
            <p> To view the location of the Processed Document Report, click the Open Report Directory button.png</p>
            <img src="assets/Bank Statement/8.1 Bank Statement - Report - Open Report Directory.png" alt=""  onclick="openModal(this.src)" style="cursor: pointer;">


            

            <p><span style="font-weight: 600; margin-bottom: 10px;">Understanding Tally Status:</span></p>    
            <p>The Tally Status column in the processed report can have two values:
            <p> <span style="font-weight: 600; color: rgb(27, 95, 27);">• Success </span> - The document was processed and entered into Tally
                without any errors.</p>

            <p> <span style="font-weight: 600;  color: rgb(233, 10, 10); ">• Skipped</span> - The Bank statement was not processed due to an error. Check the AccuVelocity Comments column for more details.</p>
            

            <h3>Step-9 Reviewing & Moving Processed Entries in Tally:</h3>

            <p><span style="font-weight: 600;">Once AccuVelocity processes your voucher, it is stored in Customer Bank Account Name. To review , follow these steps:</span></p> 

                
               
                <p>1.Go to Gateway of Tally, select Display More Reports, then Account Books, and finally Ledgers to view the available ledgers in Tally.</p>
                <img src="assets/Bank Statement/List of Voucher Entries.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >
                <p>2.Search for the Customer Bank Account Name listed in the Tally ledger and select it to view the entries.</p>
            <!-- <img src="assets/tally.png" alt=""> -->
                <p>3.Apply the start date and end date filters to view bank statement entries for the specific period.</p>
            <img src="assets/Bank Statement/Period Date Range Filter.png" onclick="openModal(this.src)" style="cursor: pointer;">
            <p>4.Review each entry made by the software. If any entry is found under the Suspense ledger, move it to the respective party ledger.
                 (In future updates, our AI will learn from your selections and automatically suggest transactions based on your past choices and patterns.)</p>
            <!-- <img src="assets/tally.png" alt=""> -->

            <span style="font-weight: 600;;">
            <p>• All done AccuVelocity has successfully processed your Bank Statements.</p>
            <p>• For further assistance, refer to the AccuVelocity Help Page</p>
            </span>
        </div>

        <div id="receiptnote" class="section">


            <h2>User Guid For Receipt Note:</h2>

            <h3>Step-1 AccuVelocity AI:</h3>
            <p>When you open Tally, you will see the AccuVelocity AI button on your Gateway of Tally.</p>
            <p>To start using AccuVelocity Software <span style="font-weight: 600;">click on the AccuVelocity AI button</span>. </p>
            <img src="assets/ReceiptNote/1. Gateway of Tally.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-2 Voucher Processing:</h3>
            <p>This will provide you with the following options:</p>
            <p>
                <ol>
                    <li>AccuVelocity Voucher Processing</li>
                    <li>AccuVelocity Activity Log</li>
                    <li>AccuVelocity Help Page</li>
                    <li>Quit</li>
                </ol>

            </p>
            <p>To begin processing your documents,<span style="font-weight: 600;">click on AccuVelocity Voucher Processing</span> .</p>
            <img src="assets/ReceiptNote/2. AccuVelocityAI Features.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-3 Select Voucher Type:</h3>
            <p><span style="font-weight: 600;">Choose Receipt Note </span>from the available voucher types.
            <p>
            <!-- <p>This is used to process documents related to goods being delivered to customers
            <p> -->
                <img src="assets/ReceiptNote/4. Accuvelocity Voucher Processing - Receipt Note.jpg" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-4 Select Document for Processing:</h3>
            <p>To process a <span style="font-weight: 600;">single Receipt Note</span> select the file from your system or specify its location.</p>
            <p>To process<span style="font-weight: 600;"> multiple Receipt Note</span> select the ZIP file path containing all documents.</p>
            <p>
                <span style="font-weight: 600; font-style: italic ;">
                    Note:
                    To provide multiple Receipt Note for processing, create a folder and add all the documents to this
                    folder.
                    Then, create a zip archive of this folder and provide the path to this zip file.
                </span>
            </p>


            <img src="assets/ReceiptNote/4.1 Accuvelocity Voucher Processing - Receipt Note.jpg" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-5 Verify File Selection & Backup</h3>
            <p>Confirm the document path you selected.</p>
            <p>It is recommended to keep the<span style="font-weight: 600;"> "Backup Before Import" </span>option set to YES to ensure a safety backup
                before inserting new vouchers.</p>
                <img src="assets/ReceiptNote/6. Bank Statement - File Path Verify & Backup Yes.jpg" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >

            <h3>Step-6 Start Processing:</h3>
            <p><span style="font-weight: 600;">Click Process Voucher</span>(top-right of Tally) or press <span style="font-weight: 600;">Ctrl + V</span> to start processing.</p>
            <img src="assets/ReceiptNote/7. Bank Statement - Start Processing Accept Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-7 Processing Confirmation</h3>
            <p><span style="font-weight: 600;">A prompt window will appear</span>, confirming that the document is being processed.</p>
            <p>Please wait for the processed report to be generated.</p>
            <img src="assets/ReceiptNote/8. BankStatment - Confirmation prompt.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-8 Verify Processed Report:</h3>
            <p>Once processing is complete, the AccuVelocity Processed Report will display the following details:
                <p>• FileName</p>
                <p>• Received Date</p>
                <p>• Vendor Name</p>
                <p>• Invoice No</p>
                <p>• Invoice Date</p>
                <p>• Total Amount</p>
                <p>• Total Pages</p>
                <p>• Estimated Time Saved</p>
                <p>• Tally Punch-in Status</p>
                <p>• Pricelist Verified</p>
                <p>• AccuVelocity Comments</p>
            </p>

            <img src="assets/ReceiptNote/8. Receipt Note Entry - Processed Document Report.png" alt=""  onclick="openModal(this.src)" style="cursor: pointer;">
            <p> To view the location of the Processed Document Report, click the Open Report Directory button.png</p>
            <img src="assets/ReceiptNote/8.1 Receipt Note Entry - To view the location of the Processed Document Report, click the Open Report Directory button.png" alt=""  onclick="openModal(this.src)" style="cursor: pointer;">


            

            <p><span style="font-weight: 600; margin-bottom: 10px;">Understanding Tally Status:</span></p>    
            <p>The Tally Status column in the processed report can have two values:
            <p> <span style="font-weight: 600; color: rgb(27, 95, 27);">• Success </span> - The document was processed and entered into Tally
                without any errors.</p>

            <p> <span style="font-weight: 600;  color: rgb(233, 10, 10); ">• Skipped</span> - The Receipt Note was not processed due to an error. Check the AccuVelocity Comments column for more details.</p>
            

            <h3>Step-9 Reviewing & Moving Processed Entries in Tally:</h3>

            <p><span style="font-weight: 600;">Once AccuVelocity processes your voucher, it is stored in Customer Voucher Type  i.e. Fee Receipt Note. To review , follow these steps:</span></p> 

                
               
                <p>1.Navigate  to Gateway of Tally, open Daybook, and filter by the date of the processed entry.</p>
                <img src="assets/Bank Statement/List of Voucher Entries.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >
                <p>2.Apply the Respective Voucher Type filter, such as Fee Receipt etc., and select a processed document voucher with the latest unique voucher number to view the detailed entry.</p>
            <!-- <img src="assets/tally.png" alt=""> -->
                <p>3.Click "Other Voucher" (located on the right-middle of Tally) or press F10 to change the voucher type.</p>
            <img src="assets/Bank Statement/Period Date Range Filter.png" onclick="openModal(this.src)" style="cursor: pointer;">
            <p>4.Press Ctrl + A to save and successfully transfer the entry to the selected voucher type.</p>
            <!-- <img src="assets/tally.png" alt=""> -->

            <span style="font-weight: 600;;">
            <p>• All done AccuVelocity has successfully processed your ReceiptNote.</p>
            <p>• For further assistance, refer to the AccuVelocity Help Page</p>
            </span>
        </div>


        <div id="purchaseorder" class="section">


            <h2>User Guid For Purchase Order:</h2>

            <h3>Step-1 AccuVelocity AI:</h3>
            <p>When you open Tally, you will see the AccuVelocity AI button on your Gateway of Tally.</p>
            <p>To start using AccuVelocity Software <span style="font-weight: 600;">click on the AccuVelocity AI button</span>. </p>
            <img src="assets/PurchaseOrder/1. Gateway of Tally.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-2 Voucher Processing:</h3>
            <p>This will provide you with the following options:</p>
            <p>
                <ol>
                    <li>AccuVelocity Voucher Processing</li>
                    <li>AccuVelocity Activity Log</li>
                    <li>AccuVelocity Help Page</li>
                    <li>Quit</li>
                </ol>

            </p>
            <p>To begin processing your documents,<span style="font-weight: 600;">click on AccuVelocity Voucher Processing</span> .</p>
            <img src="assets/PurchaseOrder/2. AccuVelocityAI Features.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-3 Select Voucher Type:</h3>
            <p><span style="font-weight: 600;">Choose Purchase Order </span>from the available voucher types.
            <p>
            <!-- <p>This is used to process documents related to goods being delivered to customers
            <p> -->
                <img src="assets/PurchaseOrder/4. Accuvelocity Voucher Processing - Purchase Order.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-4 Select Document for Processing:</h3>
            <p>To process a <span style="font-weight: 600;">single Purchase Order</span> select the file from your system or specify its location.</p>
            <p>To process<span style="font-weight: 600;"> multiple Purchase Order</span> select the ZIP file path containing multiple Excel files.</p>
            <p>
                <span style="font-weight: 600; font-style: italic ;">
                    Note:
                    To provide multiple Purchase Order for processing, create a folder and add all the documents to this
                    folder.
                    Then, create a zip archive of this folder and provide the path to this zip file.
                </span>
            </p>


            <img src="assets/PurchaseOrder/4.1 Accuvelocity Voucher Processing - Purchase Order.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-5 Verify File Selection & Backup</h3>
            <p>Confirm the document path you selected.</p>
            <p>It is recommended to keep the<span style="font-weight: 600;"> "Backup Before Import" </span>option set to YES to ensure a safety backup
                before inserting new vouchers.</p>
                <img src="assets/PurchaseOrder/6. Purchase Order - File Path Verify & Backup Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >

            <h3>Step-6 Start Processing:</h3>
            <p><span style="font-weight: 600;">Click Process Voucher</span>(top-right of Tally) or press <span style="font-weight: 600;">Ctrl + V</span> to start processing.</p>
            <img src="assets/PurchaseOrder/7. Purchase Order - Start Processing Accept Yes.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-7 Processing Confirmation</h3>
            <p><span style="font-weight: 600;">A prompt window will appear</span>, confirming that the document is being processed.</p>
            <p>Please wait for the processed report to be generated.</p>
            <img src="assets/ReceiptNote/8. BankStatment - Confirmation prompt.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;">

            <h3>Step-8 Verify Processed Report:</h3>
            <p>Once processing is complete, the AccuVelocity Processed Report will display the following details:
                <p>• PO_NO</p>
                <p>• PO_DATE</p>
                <p>• PO_VENDOR_NAME</p>
                <p>• PO_TOTAL_ITEMS</p>
                <p>• PO_ITEMS_ADDED_IN_TALLY</p>
                <p>• PO_SKIPPED_ITEMS</p>
                <p>• TIME_SAVED</p>
                <p>• AV_STATUS</p>
                <p>• AV_COMMENTS</p>
            </p>

            <img src="assets/ReceiptNote/8. Receipt Note Entry - Processed Document Report.png" alt=""  onclick="openModal(this.src)" style="cursor: pointer;">
            <p> To view the location of the Processed Document Report, click the Open Report Directory button.png</p>
            <img src="assets/ReceiptNote/8.1 Receipt Note Entry - To view the location of the Processed Document Report, click the Open Report Directory button.png" alt=""  onclick="openModal(this.src)" style="cursor: pointer;">


            

            <p><span style="font-weight: 600; margin-bottom: 10px;">Understanding Tally Status:</span></p>    
            <p>The Tally Status column in the processed report can have two values:
            <p> <span style="font-weight: 600; color: rgb(27, 95, 27);">• Success </span> - The Purchase Order was successfully processed by AccuVelocity and added to
                your Tally.</p>

            <p> <span style="font-weight: 600;  color: rgb(233, 10, 10); ">• Skipped</span> - – The Purchase Order was not processed due to an error. Check the
                AV_COMMENTS column for more details (e.g., duplicate entry detected or other validation errors).</p>
            

            <h3>Step-9 Reviewing & Moving Processed Entries in Tally:</h3>

            <p><span style="font-weight: 600;">Once AccuVelocity processes your voucher, follow these steps to review and verify:</span></p> 

                
               
                <p>1.Go to Gateway of Tally.</p>
                <img src="assets/Bank Statement/List of Voucher Entries.png" alt="" onclick="openModal(this.src)" style="cursor: pointer;" >
                <p>2.Go to Display More Reports -> Statements of Accounts -> Statistics .</p>
            <!-- <img src="assets/tally.png" alt=""> -->
                <p>3.Select Type of Voucher "AV-Purchase Order" .</p>
            <img src="assets/Bank Statement/Period Date Range Filter.png" onclick="openModal(this.src)" style="cursor: pointer;">
            <p>4.Select the specific month again to see all the PO's posted for the month and verify those PO's.</p>
            <!-- <img src="assets/tally.png" alt=""> -->
            <p>5. After verification, if everything looks fine, change the voucher type by pressing F10 and
                selecting the specific voucher type. Then press Ctrl + A to save the voucher with the updated
                voucher type.</p>
            <span style="font-weight: 600;;">
            <p>• All done AccuVelocity has successfully processed your ReceiptNote.</p>
            <p>• For further assistance, refer to the AccuVelocity Help Page</p>
            </span>
        </div>

    </div>


    <div id="faqs" class="section">
        <h2>3.FAQs</h2>


        <h3>1.Can I process scanned documents?</h3>
        <p>Yes, AccuVelocity can extract data from scanned documents. However, extraction quality depends on the
            quality of the document.</p>

        <h3>2.Will the system work if my document is protected by password?</h3>
        <p>No, it will not work if your document is protected by password. For assistance, contact support at <a
                href="<EMAIL>"><EMAIL></a></p>

        <h3>3.How can I suggest new features or improvements?</h3>
        <p>For suggesting new features, please contact support at <a
                href="<EMAIL>"><EMAIL></a></p>

        <h3>4.How do I report a problem with the extraction?</h3>
        <p>For any issues, including error messages or incorrect outputs, please contact support at <a
                href="<EMAIL>"><EMAIL></a></p>

        <h3>5.What is the approximate time taken to process or extract one document (pdf/word/jpg/png)?</h3>
        <p>Document processing times vary based on size and number of pages. Typically, it takes 30 seconds to 3
            minutes. Heavy server loads may cause delays.</p>





    </div>


    <div id="troubleshoot" class="section">

        <h2>4.Troubleshooting</h2>
        <p>Watch this troubleshooting guide:</p>
        <iframe src="https://www.youtube.com/embed/c0Ui8i37dAk" title="Troubleshooting Guide"
            allowfullscreen></iframe>
        <p><a href="https://youtu.be/c0Ui8i37dAk" target="_blank">Watch on YouTube</a></p>

    </div>


    <div id="support" class="section">
        <h2>5.Support</h2>
        <p>If you encounter any issues that you cannot resolve, contact our support team at <a
                href="<EMAIL>"><EMAIL></a>. Provide a detailed description of your
            problem, including any error messages and screenshots if applicable.
            Our resolute support team is available to assist you with any questions or issues you may have. You can
            reach us through the following channels:
        </p>
        <p> • <span style="font-weight: 600;">Email</span> :
            <a href="mailto:<EMAIL>"><EMAIL></a>


        </p>
        <p> • <span style="font-weight: 600;">Phone</span> : +91 98989 92435


        </p>
    </div>

    <div id="conclusion" class="section">
        <h2>6.Conclusion</h2>
        <p>AccuVelocity is an advanced tool designed to optimize velocity measurements and enhance data accuracy.
            By adhering to this user manual, you can fully leverage its features and ensure peak performance.
            For any further inquiries or assistance please feel free to contact our support team.
        </p>


        </p>
    </div>
</div>    

<!-- 
    <script>
        // function toggleSubmenu(id) {
        //   var submenu = document.getElementById(id);
        //   if (submenu.style.display === "none") {
        //     submenu.style.display = "block";
        //   } else {
        //     submenu.style.display = "none";
        //   }
        // }
    </script> -->

      <!-- Fullscreen modal -->
    <div id="imageModal" class="modal" onclick="closeModal()">
        <span class="close-btn" onclick="closeModal()">×</span>
        <img id="modalImage">
    </div> 

    <script>
        function openModal(src) {
            document.getElementById("modalImage").src = src;
            document.getElementById("imageModal").style.display = "flex";
        }

        function closeModal() {
            document.getElementById("imageModal").style.display = "none";
        }
    </script> 




</body>

</html>