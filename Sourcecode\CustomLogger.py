import os
import logging
import tempfile
from datetime import datetime

class CLogger:
    strLogFilePath = ""

    @classmethod
    def MCSetupLogging(cls, strLogsDirPath=r".\Logs"):
        """Sets up the logging file with timestamped filename."""
        try:
            try:
                if not os.path.exists(strLogsDirPath):
                    os.makedirs(strLogsDirPath)
            except Exception as e:
                # Fallback to Temp folder if log directory creation fails
                strLogsDirPath = tempfile.gettempdir()
                print(f"Directory creation for logs failed, Error: {e}, using Temp folder {strLogsDirPath}")
            
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            cls.strLogFilePath = os.path.join(strLogsDirPath, f"log_{current_time}.log")

            logging.basicConfig(
                filename=cls.strLogFilePath,
                level=logging.NOTSET,
                format='%(asctime)s - %(levelname)s - %(message)s'
            )
            logging.info("Logging setup initialized.")
        
        except Exception as e:
            print(f"Failed to set up logging: {e}")

    @classmethod
    def MCWriteLog(cls, strLogType, strLogMessage):
        """Logs messages based on severity."""
        try:
            if not cls.strLogFilePath:
                raise ValueError("Logger is not set up. Call MCSetupLogging() first.")
            
            log_type = strLogType.lower()
            log_methods = {
                "info": logging.info,
                "warning": logging.warning,
                "error": logging.error,
                "critical": logging.critical,
                "debug": logging.debug
            }

            if log_type in log_methods:
                log_methods[log_type](strLogMessage)
            else:
                logging.error(f"Invalid log type '{strLogType}' provided. Logging as ERROR.")
                logging.error(strLogMessage)

        except ValueError as ve:
            print(f"Logging Error: {ve}")
        except Exception as e:
            print(f"Unexpected error during logging: {e}")


if __name__ == "__main__":
    CLogger.MCSetupLogging(strLogsDirPath="Logs")
    CLogger.MCWriteLog("info", "This is a info messaege")
    CLogger.MCWriteLog("error", "This is a error messaege")
    CLogger.MCWriteLog("warning", "This is a warning messaege")
    CLogger.MCWriteLog("debug", "This is a debug messaege")
