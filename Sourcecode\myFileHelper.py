import os
import sys

class CFileHelper:
    
    
    @staticmethod
    def _get_executable_directory():
        """
        Returns the directory where the current executable or script resides.
        Ensures file lookup is relative to the EXE's location, not the invocation path.
        Includes error handling for edge cases.
        """
        try:
            if getattr(sys, 'frozen', False):  # Check if running as an EXE
                executable_path = sys.executable
                if not os.path.exists(executable_path):
                    raise FileNotFoundError(f"Executable path not found: {executable_path}")
                return os.path.dirname(executable_path)
            
            else:  # Running as a regular script
                
                return os.getcwd()
        
        except AttributeError as e:
            raise RuntimeError(f"Failed to determine the executable or script directory due to missing attribute: {e}")
        
        except Exception as e:
            raise RuntimeError(f"An unexpected error occurred while determining the directory: {e}")

