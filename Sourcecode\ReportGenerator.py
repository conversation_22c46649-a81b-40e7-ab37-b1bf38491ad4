import os
import platform
import socket
import getpass
import datetime
import json
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER
from CustomLogger import CLogger

class AccuVelocityReportGenerator:
    """
    Class to generate detailed PDF reports for AccuVelocity diagnostics.
    """

    def __init__(self, diagnostics_instance, check_results=None):
        """
        Initialize the report generator with diagnostics data.

        Args:
            diagnostics_instance: Instance of CRunDiagnostics class
            check_results: List of tuples containing (check_name, success, comment)
        """
        self.diag = diagnostics_instance
        self.check_results = check_results or []

        # Create a fresh stylesheet without modifying the built-in ones
        self.styles = {}

        # Define all styles from scratch to avoid conflicts
        self.styles['av_title'] = ParagraphStyle(
            name='av_title',
            fontName='Helvetica-Bold',
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=12
        )

        self.styles['av_subtitle'] = ParagraphStyle(
            name='av_subtitle',
            fontName='Helvetica-Bold',
            fontSize=14,
            alignment=TA_CENTER,
            spaceAfter=10
        )

        self.styles['av_section_header'] = ParagraphStyle(
            name='av_section_header',
            fontName='Helvetica-Bold',
            fontSize=12,
            spaceAfter=6
        )

        self.styles['av_normal'] = ParagraphStyle(
            name='av_normal',
            fontName='Helvetica',
            fontSize=10,
            spaceAfter=6
        )

        self.styles['av_code'] = ParagraphStyle(
            name='av_code',
            fontName='Courier',
            fontSize=8,
            leftIndent=20,
            rightIndent=20,
            spaceAfter=6
        )

        self.styles['av_success'] = ParagraphStyle(
            name='av_success',
            fontName='Helvetica',
            textColor=colors.green,
            fontSize=10
        )

        self.styles['av_error'] = ParagraphStyle(
            name='av_error',
            fontName='Helvetica',
            textColor=colors.red,
            fontSize=10
        )

        self.styles['av_warning'] = ParagraphStyle(
            name='av_warning',
            fontName='Helvetica',
            textColor=colors.orange,
            fontSize=10
        )

        self.styles['av_footer'] = ParagraphStyle(
            name='av_footer',
            fontName='Helvetica',
            fontSize=8,
            alignment=TA_CENTER
        )

    def generate_report(self, output_path):
        """
        Generate a comprehensive PDF report with all diagnostic information.

        Args:
            output_path: Path where the PDF report will be saved

        Returns:
            bool: True if report generation was successful, False otherwise
        """
        try:
            # Create the document
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=72
            )

            # Build the content
            elements = []

            # Add cover page
            self._add_cover_page(elements)

            # Add report header
            self._add_report_header(elements)

            # Add system information
            self._add_system_info(elements)

            # Add diagnostic results
            self._add_diagnostic_results(elements)

            # Add configuration details
            self._add_configuration_details(elements)

            # Add log entries
            self._add_log_entries(elements)

            # Build the document
            doc.build(elements, onFirstPage=self._add_page_number, onLaterPages=self._add_page_number)

            CLogger.MCWriteLog("info", f"Diagnostic report generated successfully at {output_path}")
            return True

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to generate diagnostic report: {e}")
            return False

    def _add_cover_page(self, elements):
        """Add a professional cover page to the report"""
        # Title
        title_style = ParagraphStyle(
            name='CoverTitle',
            fontName='Helvetica-Bold',
            fontSize=24,
            alignment=TA_CENTER,
            spaceAfter=36,
            textColor=colors.darkblue
        )
        elements.append(Spacer(1, 2*inch))
        elements.append(Paragraph("AccuVelocity", title_style))

        # Subtitle
        subtitle_style = ParagraphStyle(
            name='CoverSubtitle',
            fontName='Helvetica-Bold',
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=24,
            textColor=colors.darkblue
        )
        elements.append(Paragraph("Comprehensive Diagnostic Report", subtitle_style))

        # Date and time
        date_style = ParagraphStyle(
            name='CoverDate',
            fontName='Helvetica',
            fontSize=14,
            alignment=TA_CENTER,
            spaceAfter=72
        )
        timestamp = datetime.datetime.now().strftime("%B %d, %Y at %I:%M %p")
        elements.append(Paragraph(f"Generated on {timestamp}", date_style))

        # System information
        system_style = ParagraphStyle(
            name='CoverSystem',
            fontName='Helvetica',
            fontSize=12,
            alignment=TA_CENTER,
            spaceAfter=12
        )
        elements.append(Paragraph(f"System: {platform.node()}", system_style))
        elements.append(Paragraph(f"User: {getpass.getuser()}", system_style))

        # Summary of results
        if self.check_results:
            total_checks = len(self.check_results)
            passed_checks = sum(1 for _, success, _ in self.check_results if success)
            failed_checks = total_checks - passed_checks

            summary_style = ParagraphStyle(
                name='CoverSummary',
                fontName='Helvetica-Bold',
                fontSize=14,
                alignment=TA_CENTER,
                spaceAfter=12,
                textColor=colors.darkblue
            )
            elements.append(Spacer(1, 0.5*inch))
            elements.append(Paragraph(f"Summary of Diagnostic Results", summary_style))

            result_style_pass = ParagraphStyle(
                name='CoverResultPass',
                fontName='Helvetica',
                fontSize=12,
                alignment=TA_CENTER,
                textColor=colors.darkgreen
            )
            result_style_fail = ParagraphStyle(
                name='CoverResultFail',
                fontName='Helvetica',
                fontSize=12,
                alignment=TA_CENTER,
                textColor=colors.darkred
            )

            elements.append(Paragraph(f"Total Checks: {total_checks}", system_style))
            elements.append(Paragraph(f"Passed: {passed_checks}", result_style_pass))
            elements.append(Paragraph(f"Failed: {failed_checks}", result_style_fail))

        # Footer
        footer_style = ParagraphStyle(
            name='CoverFooter',
            fontName='Helvetica',
            fontSize=10,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        elements.append(Spacer(1, 2*inch))
        elements.append(Paragraph("This report contains detailed information about your system configuration, TallyPrime installation, and AccuVelocity integration.", footer_style))
        elements.append(Paragraph("For assistance, please contact AccuVelocity <NAME_EMAIL>", footer_style))

        # Add page break
        elements.append(PageBreak())

    def _add_report_header(self, elements):
        """Add the report header with title and timestamp"""
        # Add title
        try:
            # Try to get title from diagnostics instance if method exists
            if hasattr(self.diag, 'get_diagnostics_title') and callable(getattr(self.diag, 'get_diagnostics_title')):
                title = self.diag.get_diagnostics_title()
            else:
                title = "AccuVelocity Diagnostic Report"
        except Exception as e:
            CLogger.MCWriteLog("error", f"Error getting diagnostics title: {e}")
            title = "AccuVelocity Diagnostic Report"

        # Create a more readable title with better styling
        title_style = ParagraphStyle(
            name='enhanced_title',
            fontName='Helvetica-Bold',
            fontSize=16,
            alignment=TA_CENTER,
            spaceAfter=6,
            textColor=colors.darkblue
        )
        elements.append(Paragraph(title, title_style))

        # Add timestamp with better styling
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        subtitle_style = ParagraphStyle(
            name='enhanced_subtitle',
            fontName='Helvetica',
            fontSize=12,
            alignment=TA_CENTER,
            spaceAfter=6,
            textColor=colors.darkblue
        )
        elements.append(Paragraph(f"Report Generated: {timestamp}", subtitle_style))

        elements.append(Spacer(1, 0.25*inch))

    def _add_system_info(self, elements):
        """Add system information section"""
        elements.append(Paragraph("System Information", self.styles['av_section_header']))

        # Collect basic system info
        system_info = [
            ("Operating System", platform.platform()),
            ("System", f"{platform.system()} {platform.release()}"),
            ("Machine", platform.machine()),
            ("Processor", platform.processor()),
            ("Hostname", socket.gethostname()),
            ("Username", getpass.getuser()),
            ("Python Version", platform.python_version()),
            ("Report Time", datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        ]

        # Add more detailed system information
        try:
            import psutil

            # CPU information
            cpu_count_physical = psutil.cpu_count(logical=False)
            cpu_count_logical = psutil.cpu_count(logical=True)
            cpu_percent = psutil.cpu_percent(interval=0.1)

            # Memory information
            memory = psutil.virtual_memory()
            memory_total_gb = memory.total / (1024**3)
            memory_available_gb = memory.available / (1024**3)
            memory_used_gb = memory.used / (1024**3)
            memory_percent = memory.percent

            # Disk information
            disk = psutil.disk_usage('/')
            disk_total_gb = disk.total / (1024**3)
            disk_used_gb = disk.used / (1024**3)
            disk_free_gb = disk.free / (1024**3)
            disk_percent = disk.percent

            # Add to system_info (with requested fields removed)
            system_info.extend([
                ("", ""),  # Spacer
                ("CPU Information", ""),
                ("Physical Cores", str(cpu_count_physical)),
                ("Logical Cores", str(cpu_count_logical)),
                ("CPU Usage", f"{cpu_percent}%"),
                ("", ""),  # Spacer
                ("Memory Information", ""),
                ("Total Memory", f"{memory_total_gb:.2f} GB"),
                ("Available Memory", f"{memory_available_gb:.2f} GB"),
                ("Used Memory", f"{memory_used_gb:.2f} GB ({memory_percent}%)"),
                ("", ""),  # Spacer
                ("Disk Information", ""),
                ("Total Disk Space", f"{disk_total_gb:.2f} GB"),
                ("Used Disk Space", f"{disk_used_gb:.2f} GB ({disk_percent}%)"),
                ("Free Disk Space", f"{disk_free_gb:.2f} GB")
                # Network Information section removed as requested
            ])

            # Network interfaces section removed as requested

        except ImportError:
            system_info.append(("Detailed System Info", "psutil module not available"))
        except Exception as e:
            system_info.append(("Error", f"Failed to collect detailed system info: {str(e)}"))

        # Create table for basic system info
        table_data = []
        for item in system_info:
            if item[0] == "":  # Spacer
                table_data.append([Paragraph("&nbsp;", self.styles['av_normal']),
                                  Paragraph("&nbsp;", self.styles['av_normal'])])
            elif item[1] == "":  # Section header
                table_data.append([Paragraph(f"<b>{item[0]}</b>", self.styles['av_section_header']),
                                  Paragraph("", self.styles['av_normal'])])
            else:
                table_data.append([Paragraph(f"<b>{item[0]}</b>", self.styles['av_normal']),
                                  Paragraph(item[1], self.styles['av_normal'])])

        table = Table(table_data, colWidths=[2*inch, 3.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('PADDING', (0, 0), (-1, -1), 6),
        ]))

        elements.append(table)
        elements.append(Spacer(1, 0.25*inch))

    def _add_diagnostic_results(self, elements):
        """Add diagnostic results section"""
        elements.append(Paragraph("Diagnostic Results Summary", self.styles['av_section_header']))

        # Create table data
        table_data = [["Check", "Status", "Details"]]

        for name, success, comment in self.check_results:
            status = "✅ PASS" if success else "❌ FAIL"
            status_style = self.styles['av_success'] if success else self.styles['av_error']

            # Clean up the comment for PDF display
            if comment:
                # Remove emoji and format for PDF
                comment = comment.replace("✓", "").replace("❌", "").strip()
                if comment.startswith("Error:"):
                    comment = comment[6:].strip()
            else:
                comment = "No details available"

            table_data.append([
                Paragraph(name, self.styles['av_normal']),
                Paragraph(status, status_style),
                Paragraph(comment, self.styles['av_normal'])
            ])

        # Create table
        table = Table(table_data, colWidths=[2*inch, 1*inch, 3*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('PADDING', (0, 0), (-1, -1), 6),
        ]))

        elements.append(table)
        elements.append(Spacer(1, 0.25*inch))

        # Add detailed internet connectivity section
        self._add_internet_connectivity_details(elements)

        elements.append(PageBreak())

    def _add_internet_connectivity_details(self, elements):
        """Add detailed internet connectivity information"""
        elements.append(Paragraph("Internet Connectivity Details", self.styles['av_section_header']))

        # Get internet connectivity check results
        internet_check = next((result for result in self.check_results if "Internet Connection Status" in result[0]), None)
        speed_test = next((result for result in self.check_results if "Internet Speed Test" in result[0]), None)

        if internet_check:
            _, success, comment = internet_check

            # Clean up the comment for PDF display
            if comment:
                # Remove emoji and format for PDF
                comment = comment.replace("✓", "").replace("❌", "").strip()
                if comment.startswith("Error:"):
                    comment = comment[6:].strip()
            else:
                comment = "No details available"

            status_text = "Connected ✅" if success else "Not Connected ❌"
            status_style = self.styles['av_success'] if success else self.styles['av_error']

            # Extract response time if available
            response_time = "N/A"
            if "response time of" in comment:
                try:
                    response_time = comment.split("response time of")[1].split("ms")[0].strip() + " ms"
                except:
                    pass

            # Create connectivity table
            conn_table_data = [
                ["Status", "Response Time", "Details"],
                [
                    Paragraph(status_text, status_style),
                    Paragraph(response_time, self.styles['av_normal']),
                    Paragraph(comment, self.styles['av_normal'])
                ]
            ]

            conn_table = Table(conn_table_data, colWidths=[1.5*inch, 1.5*inch, 3*inch])
            conn_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('PADDING', (0, 0), (-1, -1), 6),
            ]))

            elements.append(conn_table)
            elements.append(Spacer(1, 0.25*inch))

        # Add speed test results if available
        if speed_test:
            elements.append(Paragraph("Internet Speed Test Results", self.styles['av_section_header']))

            _, success, comment = speed_test

            # Clean up the comment for PDF display
            if comment:
                # Remove emoji and format for PDF
                comment = comment.replace("✓", "").replace("❌", "").strip()
                if comment.startswith("Error:"):
                    comment = comment[6:].strip()
            else:
                comment = "No details available"

            # Extract download and upload speeds if available
            download_speed = "N/A"
            upload_speed = "N/A"

            # Check if we need to get the speed values directly from the diagnostics instance
            if not comment or comment == "No details available":
                try:
                    # Try to access the last speed test results from the diagnostics instance
                    if hasattr(self.diag, 'last_speed_test_results'):
                        download = self.diag.last_speed_test_results.get('download', 0)
                        upload = self.diag.last_speed_test_results.get('upload', 0)
                        if download > 0:
                            download_speed = f"{download:.2f} Mbps"
                        if upload > 0:
                            upload_speed = f"{upload:.2f} Mbps"
                    # If no stored results, try to run a quick speed test
                    elif hasattr(self.diag, 'internet_speed_test'):
                        # Create a temporary comment with the speed values
                        CLogger.MCWriteLog("info", "Getting speed test values for report...")
                        try:
                            import speedtest
                            st = speedtest.Speedtest()
                            st.get_best_server()
                            download = st.download() / 1e6  # Convert to Mbps
                            upload = st.upload() / 1e6  # Convert to Mbps
                            download_speed = f"{download:.2f} Mbps"
                            upload_speed = f"{upload:.2f} Mbps"
                            # Update the comment with the speed values
                            if comment == "No details available":
                                comment = f"Real-Time Internet Speed Test check completed successfully."
                        except Exception as e:
                            CLogger.MCWriteLog("error", f"Error getting speed test values: {e}")
                except Exception as e:
                    CLogger.MCWriteLog("error", f"Error accessing speed test results: {e}")

            # If we still have speed values in the comment, extract them
            if "Download:" in comment and "Mbps" in comment:
                try:
                    download_part = comment.split("Download:")[1].split("Mbps")[0].strip()
                    download_speed = download_part + " Mbps"
                except:
                    pass

            if "Upload:" in comment and "Mbps" in comment:
                try:
                    upload_part = comment.split("Upload:")[1].split("Mbps")[0].strip()
                    upload_speed = upload_part + " Mbps"
                except:
                    pass

            # Create speed test table
            speed_table_data = [
                ["Download Speed", "Upload Speed", "Assessment"],
                [
                    Paragraph(download_speed, self.styles['av_normal']),
                    Paragraph(upload_speed, self.styles['av_normal']),
                    Paragraph(comment, self.styles['av_normal'] if success else self.styles['av_error'])
                ]
            ]

            speed_table = Table(speed_table_data, colWidths=[2*inch, 2*inch, 2*inch])
            speed_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('PADDING', (0, 0), (-1, -1), 6),
            ]))

            elements.append(speed_table)
            elements.append(Spacer(1, 0.25*inch))

    def _add_configuration_details(self, elements):
        """Add configuration details section"""
        elements.append(Paragraph("Configuration Details", self.styles['av_section_header']))

        # Add TallyPrime details
        self._add_tallyprime_details(elements)

        # Add configuration files content
        self._add_config_files_content(elements)

        elements.append(PageBreak())

    def _add_tallyprime_details(self, elements):
        """Add TallyPrime installation details"""
        elements.append(Paragraph("TallyPrime Installations", self.styles['av_section_header']))

        try:
            # Get TallyPrime installations if method exists
            if hasattr(self.diag, 'find_tallyprime_installations') and callable(getattr(self.diag, 'find_tallyprime_installations')):
                installations = self.diag.find_tallyprime_installations()
            else:
                # Fallback if method doesn't exist
                CLogger.MCWriteLog("warning", "find_tallyprime_installations method not found in diagnostics instance")
                elements.append(Paragraph("Unable to retrieve TallyPrime installation details - method not available.", self.styles['av_warning']))
                return

            if not installations:
                elements.append(Paragraph("No TallyPrime installations found.", self.styles['av_warning']))
                return
        except Exception as e:
            CLogger.MCWriteLog("error", f"Error finding TallyPrime installations: {e}")
            elements.append(Paragraph(f"Error finding TallyPrime installations: {e}", self.styles['av_error']))
            return

        # Create table data
        table_data = [["Installation Path", "Version", "Status"]]

        # Define supported versions
        supported_versions = ["1.1.5.1", "1.1.4.1", "1.1.6.0"]

        for path, version in installations:
            status = "Supported ✅" if version in supported_versions else "Not Supported ❌"
            status_style = self.styles['av_success'] if version in supported_versions else self.styles['av_error']

            table_data.append([
                Paragraph(path, self.styles['av_normal']),
                Paragraph(version, self.styles['av_normal']),
                Paragraph(status, status_style)
            ])

        # Create table
        table = Table(table_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('PADDING', (0, 0), (-1, -1), 6),
        ]))

        elements.append(table)
        elements.append(Spacer(1, 0.25*inch))

        # Add TallyPrime server status
        elements.append(Paragraph("TallyPrime Server Status", self.styles['av_section_header']))

        try:
            # Check if method exists
            if hasattr(self.diag, 'check_tally_prime_server') and callable(getattr(self.diag, 'check_tally_prime_server')):
                # Check TallyPrime server status
                success, comment = self.diag.check_tally_prime_server()

                # Clean up the comment for PDF display
                if comment:
                    # Remove emoji and format for PDF
                    comment = comment.replace("✓", "").replace("❌", "").strip()
                    if comment.startswith("Error:"):
                        comment = comment[6:].strip()
                else:
                    comment = "No details available"

                status_text = "Running ✅" if success else "Not Running ❌"
                status_style = self.styles['av_success'] if success else self.styles['av_error']

                server_table_data = [
                    ["Status", "Details"],
                    [Paragraph(status_text, status_style), Paragraph(comment, self.styles['av_normal'])]
                ]

                server_table = Table(server_table_data, colWidths=[1.5*inch, 4.5*inch])
                server_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('PADDING', (0, 0), (-1, -1), 6),
                ]))

                elements.append(server_table)
                elements.append(Spacer(1, 0.25*inch))
            else:
                # Fallback if method doesn't exist
                CLogger.MCWriteLog("warning", "check_tally_prime_server method not found in diagnostics instance")
                elements.append(Paragraph("Unable to check TallyPrime server status - method not available.", self.styles['av_warning']))

        except Exception as e:
            CLogger.MCWriteLog("error", f"Error checking TallyPrime server status: {e}")
            elements.append(Paragraph(f"Error checking TallyPrime server status: {str(e)}", self.styles['av_error']))

        # Add TallyPrime license status
        elements.append(Paragraph("TallyPrime License Status", self.styles['av_section_header']))

        try:
            # Check if method exists
            if hasattr(self.diag, 'check_license_file') and callable(getattr(self.diag, 'check_license_file')):
                # Check license file
                success, comment = self.diag.check_license_file()

                # Clean up the comment for PDF display
                if comment:
                    # Remove emoji and format for PDF
                    comment = comment.replace("✓", "").replace("❌", "").strip()
                    if comment.startswith("Error:"):
                        comment = comment[6:].strip()
                else:
                    comment = "No details available"

                status_text = "Valid License ✅" if success else "License Issue ❌"
                status_style = self.styles['av_success'] if success else self.styles['av_error']

                license_table_data = [
                    ["Status", "Details"],
                    [Paragraph(status_text, status_style), Paragraph(comment, self.styles['av_normal'])]
                ]

                license_table = Table(license_table_data, colWidths=[1.5*inch, 4.5*inch])
                license_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('PADDING', (0, 0), (-1, -1), 6),
                ]))

                elements.append(license_table)
                elements.append(Spacer(1, 0.25*inch))
            else:
                # Fallback if method doesn't exist
                CLogger.MCWriteLog("warning", "check_license_file method not found in diagnostics instance")
                elements.append(Paragraph("Unable to check TallyPrime license status - method not available.", self.styles['av_warning']))

        except Exception as e:
            CLogger.MCWriteLog("error", f"Error checking TallyPrime license status: {e}")
            elements.append(Paragraph(f"Error checking TallyPrime license status: {str(e)}", self.styles['av_error']))

    def _format_config_content(self, content):
        """Format configuration file content for better readability"""
        # Create a table with one column for better formatting
        content_lines = content.split('\n')
        table_data = []

        for line in content_lines:
            if line.strip():  # Skip empty lines
                table_data.append([Paragraph(line, self.styles['av_code'])])

        if table_data:
            table = Table(table_data, colWidths=[5.5*inch])
            table.setStyle(TableStyle([
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 12),
                ('RIGHTPADDING', (0, 0), (-1, -1), 12),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 2),
                ('TOPPADDING', (0, 0), (-1, -1), 2),
            ]))
            return table
        return None

    def _add_config_files_content(self, elements):
        """Add configuration files content"""
        try:
            # Get the first TallyPrime installation
            installations = self.diag.find_tallyprime_installations()

            if not installations:
                elements.append(Paragraph("No TallyPrime installations found to extract configuration files.", self.styles['av_warning']))
                return

            exe_path, _ = installations[0]
            base_dir = os.path.dirname(exe_path)

            # Add tally.ini content
            ini_path = os.path.join(base_dir, 'tally.ini')
            if os.path.isfile(ini_path):
                elements.append(Paragraph("tally.ini Content", self.styles['av_section_header']))
                try:
                    with open(ini_path, 'r', errors='replace') as f:  # Added error handling for encoding issues
                        content = f.read()
                    # Escape any special characters that might cause issues in PDF
                    content = content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

                    # Use the formatting helper method for better readability
                    formatted_table = self._format_config_content(content)
                    if formatted_table:
                        elements.append(formatted_table)
                    else:
                        # Fallback to original method if formatting fails
                        elements.append(Paragraph(content, self.styles['av_code']))

                    elements.append(Spacer(1, 0.25*inch))
                except Exception as e:
                    CLogger.MCWriteLog("error", f"Error reading tally.ini: {e}")
                    elements.append(Paragraph(f"Error reading tally.ini: {e}", self.styles['av_error']))
            else:
                elements.append(Paragraph("tally.ini file not found.", self.styles['av_warning']))

            # Add Config.xml content
            config_xml = os.path.join(base_dir, 'Config.xml')
            if os.path.isfile(config_xml):
                elements.append(Paragraph("Config.xml Content", self.styles['av_section_header']))
                try:
                    with open(config_xml, 'r', errors='replace') as f:  # Added error handling for encoding issues
                        content = f.read()
                    # Escape any special characters that might cause issues in PDF
                    content = content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

                    # Use the formatting helper method for better readability
                    formatted_table = self._format_config_content(content)
                    if formatted_table:
                        elements.append(formatted_table)
                    else:
                        # Fallback to original method if formatting fails
                        elements.append(Paragraph(content, self.styles['av_code']))

                    elements.append(Spacer(1, 0.25*inch))
                except Exception as e:
                    CLogger.MCWriteLog("error", f"Error reading Config.xml: {e}")
                    elements.append(Paragraph(f"Error reading Config.xml: {e}", self.styles['av_error']))
            else:
                elements.append(Paragraph("Config.xml file not found.", self.styles['av_warning']))

            # Add UserConfig.json content
            try:
                from CustomHelper import CResoucedataHelper
                user_config_path = CResoucedataHelper.MSGetUserConfigLocation()
                if user_config_path and os.path.isfile(user_config_path):
                    elements.append(Paragraph("UserConfig.json Content", self.styles['av_section_header']))
                    try:
                        with open(user_config_path, 'r', errors='replace') as f:  # Added error handling for encoding issues
                            content = json.dumps(json.load(f), indent=2)
                        # Escape any special characters that might cause issues in PDF
                        content = content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

                        # Use the formatting helper method for better readability
                        formatted_table = self._format_config_content(content)
                        if formatted_table:
                            elements.append(formatted_table)
                        else:
                            # Fallback to original method if formatting fails
                            elements.append(Paragraph(content, self.styles['av_code']))

                        elements.append(Spacer(1, 0.25*inch))
                    except Exception as e:
                        CLogger.MCWriteLog("error", f"Error reading UserConfig.json: {e}")
                        elements.append(Paragraph(f"Error reading UserConfig.json: {e}", self.styles['av_error']))
                else:
                    elements.append(Paragraph("UserConfig.json file not found.", self.styles['av_warning']))
            except Exception as e:
                CLogger.MCWriteLog("error", f"Error accessing UserConfig.json: {e}")
                elements.append(Paragraph(f"Error accessing UserConfig.json: {e}", self.styles['av_error']))

            # Add AccuvelocityConfig.json content
            try:
                config_path = self.diag.user_config.get("ExportRequest", {}).get("VersionInfo", "")
                if config_path and os.path.isfile(config_path):
                    elements.append(Paragraph("AccuvelocityConfig.json Content", self.styles['av_section_header']))
                    try:
                        with open(config_path, 'r', errors='replace') as f:  # Added error handling for encoding issues
                            content = json.dumps(json.load(f), indent=2)
                        # Escape any special characters that might cause issues in PDF
                        content = content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

                        # Use the formatting helper method for better readability
                        formatted_table = self._format_config_content(content)
                        if formatted_table:
                            elements.append(formatted_table)
                        else:
                            # Fallback to original method if formatting fails
                            elements.append(Paragraph(content, self.styles['av_code']))

                        elements.append(Spacer(1, 0.25*inch))
                    except Exception as e:
                        CLogger.MCWriteLog("error", f"Error reading AccuvelocityConfig.json: {e}")
                        elements.append(Paragraph(f"Error reading AccuvelocityConfig.json: {e}", self.styles['av_error']))
                else:
                    elements.append(Paragraph("AccuvelocityConfig.json file not found or path not specified.", self.styles['av_warning']))
            except Exception as e:
                CLogger.MCWriteLog("error", f"Error accessing AccuvelocityConfig.json: {e}")
                elements.append(Paragraph(f"Error accessing AccuvelocityConfig.json: {e}", self.styles['av_error']))

        except Exception as e:
            CLogger.MCWriteLog("error", f"Error in _add_config_files_content: {e}")
            elements.append(Paragraph(f"An error occurred while collecting configuration files: {e}", self.styles['av_error']))

    def _add_log_entries(self, elements):
        """Add recent log entries"""
        elements.append(Paragraph("Recent Log Entries", self.styles['av_section_header']))

        try:
            # Try to get log file path
            log_file = None

            # First try the standard location
            try:
                log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Logs")
                if os.path.isdir(log_dir):
                    log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
                    if log_files:
                        # Get the most recent log file
                        log_files.sort(reverse=True)
                        log_file = os.path.join(log_dir, log_files[0])
                        elements.append(Paragraph(f"Log file found: {log_file}", self.styles['av_normal']))
            except Exception as e:
                CLogger.MCWriteLog("error", f"Error finding log files in standard location: {e}")
                elements.append(Paragraph(f"Error finding log files in standard location: {e}", self.styles['av_error']))

            # If not found, try alternative locations
            if not log_file or not os.path.isfile(log_file):
                try:
                    # Try the current directory
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    for root, dirs, _ in os.walk(current_dir):  # Using _ to ignore unused files variable
                        if "Logs" in dirs:
                            log_dir = os.path.join(root, "Logs")
                            log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
                            if log_files:
                                # Get the most recent log file
                                log_files.sort(reverse=True)
                                log_file = os.path.join(log_dir, log_files[0])
                                elements.append(Paragraph(f"Log file found in alternative location: {log_file}", self.styles['av_normal']))
                                break
                except Exception as e:
                    CLogger.MCWriteLog("error", f"Error finding log files in alternative locations: {e}")
                    elements.append(Paragraph(f"Error finding log files in alternative locations: {e}", self.styles['av_error']))

            if log_file and os.path.isfile(log_file):
                try:
                    # Read the last 100 lines of the log file
                    with open(log_file, 'r', errors='replace') as f:  # Added error handling for encoding issues
                        lines = f.readlines()
                        last_lines = lines[-100:] if len(lines) > 100 else lines

                    # Format and escape the log content
                    log_content = "".join(last_lines)
                    log_content = log_content.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

                    # Add a header with the log file name and timestamp
                    elements.append(Paragraph(f"Contents of {os.path.basename(log_file)} (last 100 lines):", self.styles['av_normal']))
                    elements.append(Spacer(1, 0.1*inch))

                    # Use the formatting helper method for better readability
                    formatted_table = self._format_config_content(log_content)
                    if formatted_table:
                        elements.append(formatted_table)
                    else:
                        # Fallback to original method if formatting fails
                        elements.append(Paragraph(log_content, self.styles['av_code']))
                except Exception as e:
                    CLogger.MCWriteLog("error", f"Error reading log file: {e}")
                    elements.append(Paragraph(f"Error reading log file: {e}", self.styles['av_error']))
            else:
                elements.append(Paragraph("No log files found. This may be normal in a test environment.", self.styles['av_warning']))

        except Exception as e:
            CLogger.MCWriteLog("error", f"Error in _add_log_entries: {e}")
            elements.append(Paragraph(f"An error occurred while collecting log entries: {e}", self.styles['av_error']))

    def _add_page_number(self, canvas, doc):
        """Add page number to each page (except cover page)"""
        page_num = canvas.getPageNumber()

        # Skip page numbering for the cover page (page 1)
        if page_num > 1:
            canvas.saveState()
            canvas.setFont('Helvetica', 8)

            # Add footer with page number
            footer_text = f"AccuVelocity Diagnostic Report - Page {page_num-1}"  # Adjust page number to account for cover
            canvas.drawCentredString(doc.width/2.0 + doc.leftMargin, 0.5*inch, footer_text)

            # Add timestamp in the footer
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            canvas.drawRightString(doc.width + doc.leftMargin - 10, 0.5*inch, timestamp)

            canvas.restoreState()
