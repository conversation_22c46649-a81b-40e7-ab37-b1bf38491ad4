import argparse
import json
import uuid
import jwt
import os
from cryptography.fernet import <PERSON><PERSON><PERSON>
from CustomLogger import <PERSON><PERSON>ogger


# Secret key for encoding and decoding the JWT
import base64
SECRET_KEY = base64.urlsafe_b64encode(b'ea7634b4c4b4c23a42218d4b0c814869')

# Generate License File
def create_license_file(user_data, output_file=r".\license.lic"):
    try:
        # Extract MAC Address
        mac_address = hex(uuid.getnode()).replace('0x', '').upper()
        
        # JWT Payload
        payload = {
            "uid": user_data['uid'],
            "name": user_data['name'],
            "mac_address": mac_address,
            "role": user_data['role'],
        }

        # Create JWT Token
        token = jwt.encode(payload, SECRET_KEY, algorithm="HS256")

        cipher_suite = Fernet(SECRET_KEY)
        encrypted_license = cipher_suite.encrypt(token.encode())

        # Save License File
        with open(output_file, "wb") as license_file:
            license_file.write(encrypted_license)

        CLogger.MCWriteLog("info", f"License file '{output_file}' created successfully.")
    
    except Exception as e:
        CLogger.MCWriteLog("error", f"Error generating license: {e}")


# Command Line Argument Parsing
def main():
    
    CLogger.MCSetupLogging(strLogsDirPath=r".\Logs")

    parser = argparse.ArgumentParser(description="License Generator")
    parser.add_argument("json_file", type=str, help="Path to the user information JSON file")
    args = parser.parse_args()

    # Read JSON File
    if not os.path.exists(args.json_file):
        print("Error: user information not found.")
        return
    
    with open(args.json_file, "r") as file:
        user_data = json.load(file)
        create_license_file(user_data)

if __name__ == "__main__":
    main()