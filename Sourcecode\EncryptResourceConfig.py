import argparse
import os
import sys
from cryptography.fernet import Fernet

# Default encryption key
DEFAULT_KEY = "-lNo9xkpKCDwflcEaa_aA2hgJeej4szEcMC6MyUewJI="

class CReadDeveloperConfig:
    @staticmethod
    def MSEncryptDeveloperConfig(file_path, key=DEFAULT_KEY, verbose=False):
        try:
            cipher = Fernet(key.encode())
            
            with open(file_path, 'rb') as file:
                file_data = file.read()

            encrypted_data = cipher.encrypt(file_data)
            
            with open(file_path, 'wb') as file:
                file.write(encrypted_data)
            
            if verbose:
                print(f"Successfully encrypted: {file_path}")
            return key
        except Exception as e:
            if verbose:
                print(f"Error encrypting {file_path}: {e}")
            return None

    @staticmethod
    def MSDecryptDeveloperConfig(file_path, key=DEFAULT_KEY, output_file=None, verbose=False):
        try:
            cipher = Fernet(key.encode())
            
            with open(file_path, 'rb') as file:
                bytes_encrypted_data = file.read()
            
            bytes_decrypted_data = cipher.decrypt(bytes_encrypted_data)
            
            if output_file:
                with open(output_file, 'wb') as file:
                    file.write(bytes_decrypted_data)
                if verbose:
                    print(f"Successfully decrypted and saved to: {output_file}")
                return None
            
            if verbose:
                print(f"Successfully decrypted: {file_path}")
            return bytes_decrypted_data
        except Exception as e:
            if verbose:
                print(f"Error decrypting {file_path}: {e}")
            return None

def process_folder(folder_path, key=DEFAULT_KEY, action="encrypt", output_file=None, verbose=False):
    if not os.path.isdir(folder_path):
        if verbose:
            print("Invalid folder path.")
        return
    
    for file_name in os.listdir(folder_path):
        file_path = os.path.join(folder_path, file_name)
        if action == "encrypt":
            CReadDeveloperConfig.MSEncryptDeveloperConfig(file_path, key, verbose)
        elif action == "decrypt":
            CReadDeveloperConfig.MSDecryptDeveloperConfig(file_path, key, output_file, verbose)

def main():
    parser = argparse.ArgumentParser(description="Encrypt or decrypt files.")
    parser.add_argument("--action", choices=["encrypt", "decrypt"], required=True, help="Specify whether to encrypt or decrypt.")
    parser.add_argument("--path", required=True, help="Path to the file or folder.")
    parser.add_argument("--key", help="Encryption/Decryption key. Defaults to a predefined key.")
    parser.add_argument("--output", help="Path to save the decrypted file. If not provided, decrypted data is returned.")
    parser.add_argument("--silent", action='store_true', help="Suppress output messages.")
    args = parser.parse_args()
    
    verbose = not args.silent
    key = args.key or DEFAULT_KEY
    
    if os.path.isdir(args.path):
        process_folder(args.path, key, args.action, args.output, verbose)
    elif os.path.isfile(args.path):
        if args.action == "encrypt":
            CReadDeveloperConfig.MSEncryptDeveloperConfig(args.path, key, verbose)
        elif args.action == "decrypt":
            decrypted_data = CReadDeveloperConfig.MSDecryptDeveloperConfig(args.path, key, args.output, verbose)
            if decrypted_data and args.output is None:
                print(decrypted_data.decode())  # Print the decrypted content if no output file is specified
    else:
        if verbose:
            print("Invalid path. Please specify a valid file or folder.")

if __name__ == "__main__":
    main()
    # pass

    # python Sourcecode\EncryptResourceConfig.py --action decrypt --path "Resources\DeveloperConfig.json" --output "Resources\DeveloperConfig.json"
    # python Sourcecode\EncryptResourceConfig.py --action decrypt --path "Resources\DeveloperConfig - RDPGPU.json" --output "Resources\DeveloperConfig - RDPGPU.json"
    # python Sourcecode\EncryptResourceConfig.py --action decrypt --path "Resources\DeveloperConfig_Server2.json" --output "Resources\DeveloperConfig_Server2.json" 
    # python Sourcecode\EncryptResourceConfig.py --action decrypt --path "Resources\DeveloperConfig_WinServer.json" --output "Resources\DeveloperConfig_WinServer.json" 
    # python Sourcecode\EncryptResourceConfig.py --action decrypt --path "Resources\DeveloperConfig_Server2 - D8025.json" --output "Resources\DeveloperConfig_Server2 - D8025.json" 


    # python Sourcecode\EncryptResourceConfig.py --action encrypt --path "Resources\DeveloperConfig.json" --output "Resources\DeveloperConfig.json"
    # python Sourcecode\EncryptResourceConfig.py --action encrypt --path "Resources\DeveloperConfig_Server2.json" --output "Resources\DeveloperConfig_Server2.json"
    # python Sourcecode\EncryptResourceConfig.py --action encrypt --path "Resources\DeveloperConfig_Server2 - P8034.json" --output "Resources\DeveloperConfig_Server2 - P8034.json"
    # python Sourcecode\EncryptResourceConfig.py --action encrypt --path "Resources\DeveloperConfig_Server2 - D8025.json" --output "Resources\DeveloperConfig_Server2 - D8025.json"
    # python Sourcecode\EncryptResourceConfig.py --action encrypt --path "Resources\DeveloperConfig_Server2 - D8024.json" --output "Resources\DeveloperConfig_Server2 - D8024.json"
    