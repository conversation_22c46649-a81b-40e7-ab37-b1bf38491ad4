import sys
import os
import datetime
import platform
import socket
import getpass
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QProgressBar,
    QLabel, QApplication, QTextEdit, QHeaderView, QDialog, QPushButton, QAbstractScrollArea,
    QDesktopWidget, QLineEdit, QFileDialog, QMessageBox, QFrame
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont, QColor

# Assuming these are custom modules you have
from CustomLogger import CLogger
from CustomHelper import CRunDiagnostics
from ReportGenerator import AccuVelocityReportGenerator

class AccuVelocityActionCommentWindow(QDialog):
    def __init__(self, comment_text, parent=None, is_success=False):
        super().__init__(parent)
        self.setWindowTitle("AccuVelocity Diagnostic Details")
        self.setGeometry(400, 400, 700, 400)
        self.main_layout = QVBoxLayout(self)

        # Determine if this is a success or error message
        self.is_success = is_success
        if comment_text.startswith("✓") or comment_text.startswith("✅"):
            self.is_success = True

        # Header with icon
        header_layout = QHBoxLayout()

        # Icon label
        icon_label = QLabel(self)
        if self.is_success:
            icon_text = "✅"
            header_text = "Diagnostic Check Passed"
            bg_color = "#d4edda"      # Light green
        else:
            icon_text = "❌"
            header_text = "Diagnostic Check Failed"
            bg_color = "#f8d7da"      # Light red

        icon_label.setText(icon_text)
        icon_font = icon_label.font()
        icon_font.setPointSize(24)
        icon_label.setFont(icon_font)
        header_layout.addWidget(icon_label)

        # Header text
        header_label = QLabel(header_text, self)
        header_font = header_label.font()
        header_font.setPointSize(16)
        header_font.setBold(True)
        header_label.setFont(header_font)
        header_layout.addWidget(header_label)
        header_layout.addStretch()

        # Add header to main layout
        self.main_layout.addLayout(header_layout)

        # Horizontal line separator
        line = QFrame(self)
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        self.main_layout.addWidget(line)

        # Display the full comment with improved formatting
        self.comment_display = QTextEdit(self)
        self.comment_display.setReadOnly(True)
        self.comment_display.setMinimumHeight(250)

        # Format the comment text
        formatted_text = self.format_comment_text(comment_text)
        self.comment_display.setHtml(formatted_text)

        # Set font and styling
        font = self.comment_display.font()
        font.setPointSize(font.pointSize() + 2)
        self.comment_display.setFont(font)

        # Set background color based on status
        self.comment_display.setStyleSheet(f"background-color: {bg_color}; border-radius: 10px; padding: 10px;")

        self.main_layout.addWidget(self.comment_display)

        # Button layout
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # Add close button
        self.close_button = QPushButton("Close", self)
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;  /* Blue color */
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 10px;
                border: none;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #1E88E5;  /* Darker blue on hover */
            }
            QPushButton:pressed {
                background-color: #1976D2;  /* Even darker blue when pressed */
            }
        """)
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)

        # Add help button if it's an error
        if not self.is_success:
            self.help_button = QPushButton("Get Help", self)
            self.help_button.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;  /* Green color */
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 10px 20px;
                    border-radius: 10px;
                    border: none;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #218838;  /* Darker green on hover */
                }
                QPushButton:pressed {
                    background-color: #1e7e34;  /* Even darker green when pressed */
                }
            """)
            self.help_button.clicked.connect(self.show_help)
            button_layout.addWidget(self.help_button)

        self.main_layout.addLayout(button_layout)

        # Center the window on the screen
        self.center()

    def format_comment_text(self, text):
        """Format the comment text with HTML for better readability"""
        # Replace newlines with HTML breaks
        text = text.replace('\n', '<br>')

        # Highlight important parts
        text = text.replace('Error:', '<span style="color: #dc3545; font-weight: bold;">Error:</span>')
        text = text.replace('Warning:', '<span style="color: #ffc107; font-weight: bold;">Warning:</span>')
        text = text.replace('Note:', '<span style="color: #17a2b8; font-weight: bold;">Note:</span>')
        text = text.replace('TODO:', '<span style="color: #007bff; font-weight: bold;">TODO:</span>')

        # Format steps or numbered lists
        if "1." in text and "2." in text:
            lines = text.split('<br>')
            for i, line in enumerate(lines):
                if line.strip().startswith(("1.", "2.", "3.", "4.", "5.")):
                    number, rest = line.split(".", 1)
                    lines[i] = f'<span style="font-weight: bold;">{number}.</span>{rest}'
            text = '<br>'.join(lines)

        return f'<div style="font-family: Arial, sans-serif; line-height: 1.5;">{text}</div>'

    def show_help(self):
        """Show help information for the error"""
        QMessageBox.information(
            self,
            "AccuVelocity Support",
            "For assistance with this issue, please contact AccuVelocity support:\n\n"
            "Email: <EMAIL>\n"
            "Phone: +91 98989 42935\n"
            "Website: https://www.accuvelocity.com/#StillHaveAQuestion"
        )

    def center(self):
        """Center the window on the screen."""
        screen_geometry = QDesktopWidget().availableGeometry()
        window_geometry = self.frameGeometry()
        center_point = screen_geometry.center()
        window_geometry.moveCenter(center_point)
        self.move(window_geometry.topLeft())

class DiagnosticsThread(QThread):
    update_status = pyqtSignal(int, bool, str)  # Updated to include comment
    current_check = pyqtSignal(str)
    finished = pyqtSignal()  # Changed to no arguments since comments are handled per check

    def __init__(self, diag, tally_path=None, parent=None):
        super().__init__(parent)
        self.diag = diag
        self.tally_path = tally_path

    def run(self):
        # Create search_paths list if tally_path is provided
        search_paths = None
        if self.tally_path and os.path.exists(self.tally_path):
            search_paths = [self.tally_path]
            CLogger.MCWriteLog("info", f"Using user-provided TallyPrime path: {self.tally_path}")
        else:
            # Try to find TallyPrime in common locations
            CLogger.MCWriteLog("info", "No valid TallyPrime path provided, searching common locations...")
            common_paths = [
                r"C:\Program Files\TallyPrime",
                r"C:\Program Files (x86)\TallyPrime",
                r"D:\TallyPrime"
            ]
            for path in common_paths:
                if os.path.isdir(path) and os.path.isfile(os.path.join(path, "tally.exe")):
                    search_paths = [path]
                    CLogger.MCWriteLog("info", f"Found TallyPrime installation at: {path}")
                    break

            # If still not found, try using the where command on Windows
            if not search_paths:
                try:
                    CLogger.MCWriteLog("info", "Attempting to locate tally.exe using system search...")
                    import subprocess
                    output = subprocess.check_output(["where", "tally.exe"], stderr=subprocess.DEVNULL, text=True)
                    if output:
                        tally_path = os.path.dirname(output.splitlines()[0])
                        search_paths = [tally_path]
                        CLogger.MCWriteLog("info", f"Found TallyPrime installation using system search: {tally_path}")
                except Exception as e:
                    CLogger.MCWriteLog("warning", f"Could not locate TallyPrime using system search: {e}")

        check_methods = [
            ("check_internet_connectivity", []),
            ("internet_speed_test", []),
            ("ram_usage_analysis", []),
            ("cpu_usage_analysis", []),
            ("disk_usage_analysis", []),
            ("check_static_ip", []),
            ("check_and_update_dynamic_ip", []),
            ("check_accuvelocity_server", []),
            ("check_tallyprime_version", [{"search_paths": search_paths}] if search_paths else []),
            ("check_tallyprime_config", [{"search_paths": search_paths}] if search_paths else []),
            ("check_tally_prime_server", []),
            ("check_license_file", [{"search_paths": search_paths}] if search_paths else []),
            ("check_important_files_and_folders", [{"search_paths": search_paths}] if search_paths else []),
        ]
        check_names = [
            "🌐 Internet Connection Status",
            "🚀 Real-Time Internet Speed Test",
            "🧠 Memory (RAM) Health Check",
            "⚙️ Processor (CPU) Performance Monitor",
            "💾 Storage & Disk Space Analysis",
            "📡 Static IP Configuration Status",
            "🔄 Dynamic IP Status & Refresh",
            "🖥️ AccuVelocity Server Connectivity",
            "🔍 TallyPrime Version Verification",
            "🛠️ TallyPrime Configuration Audit",
            "🔗 TallyPrime Server Accessibility",
            "🧾 Tally License File Validation",
            "📁 Key Files & Folders Integrity Check"
        ]
        for i, (method_name, args) in enumerate(check_methods):
            self.current_check.emit(check_names[i])
            if args and isinstance(args[0], dict):
                # If we have a dictionary of keyword arguments
                success, comment = getattr(self.diag, method_name)(**args[0])
            else:
                # If we have positional arguments or no arguments
                success, comment = getattr(self.diag, method_name)(*args) #Exception here...
            self.update_status.emit(i, success, comment)
        self.finished.emit()

class CDiagnosticsWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.diagnostics = CRunDiagnostics()
        title = self.diagnostics.get_diagnostics_title()
        self.setWindowTitle(title)
        # self.setGeometry(300, 300, 800, 500)

        # 1) Resize to 800×500 instead of setGeometry(x,y,…)
        self.resize(800, 550)  # Increased height to accommodate the new input field

        # 2) Center on screen
        qr = self.frameGeometry()                                # get window frame
        cp = QDesktopWidget().availableGeometry().center()       # screen center
        qr.moveCenter(cp)                                        # move frame center
        self.move(qr.topLeft())                                  # position top‐left

        self.main_layout = QVBoxLayout()
        self.setLayout(self.main_layout)

        # Apply professional stylesheet inspired by the image with curved corners
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1E2A44, stop:1 #2A3A55);
                border-radius: 10px;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
            }
            QTableWidget {
                background: rgba(255, 255, 255, 0.9);
                alternate-background-color: #f0f0f0;
                selection-background-color: #d0d0d0;
                gridline-color: #cccccc;
                border-radius: 10px;
                border: 1px solid #4a6491;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 4px;
                border: 1px solid #34495e;
                font-weight: bold;
            }
            QProgressBar {
                border: 2px solid #2196F3;
                border-radius: 10px;
                text-align: center;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: rgba(255, 255, 255, 0.2);
                height: 13px;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:0.5, y2:0,
                    stop:0 #2196F3, stop:1 #063653);
                border-radius: 10px;
            }
            QTextEdit {
                background: #ffffff;
                border: 1px solid #4a6491;
                border-radius: 10px;
            }
            QLineEdit {
                background: #ffffff;
                border: 1px solid #4a6491;
                border-radius: 10px;
                padding: 5px;
                font-size: 14px;
                color: #333333;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border-radius: 10px;
                padding: 5px 10px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #1E88E5;
                border: 1px solid #0d47a1;
            }
            QPushButton:pressed {
                background-color: #1976D2;
            }
            QTableWidget::item:hover {
                background-color: #e0e0e0;
            }
        """)

        # Tally Prime Location Input Section
        tally_location_layout = QHBoxLayout()

        # Label for Tally Prime Location
        tally_location_label = QLabel("TallyPrime Installation Path:")
        tally_location_label.setFont(QFont("Arial", 11))
        tally_location_layout.addWidget(tally_location_label)

        # Input field for Tally Prime Location
        self.tally_location_input = QLineEdit()
        self.tally_location_input.setPlaceholderText("Enter TallyPrime installation path (e.g., C:\\Program Files\\TallyPrime)")
        tally_location_layout.addWidget(self.tally_location_input, 3)  # Give it more space

        # Browse button
        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self.browse_tally_location)
        tally_location_layout.addWidget(self.browse_button)

        # Validate button
        self.validate_button = QPushButton("Validate")
        self.validate_button.clicked.connect(self.validate_tally_location)
        tally_location_layout.addWidget(self.validate_button)

        # Start Diagnostics button
        self.start_button = QPushButton("Start Diagnostics")
        self.start_button.clicked.connect(self.start_diagnostics)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;  /* Green */
                font-size: 14px;
                padding: 5px 15px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        tally_location_layout.addWidget(self.start_button)

        # Add the Tally location layout to the main layout
        self.main_layout.addLayout(tally_location_layout)

        # Add instructions for TallyPrime path selection
        instructions_label = QLabel(
            "Instructions:\n"
            "1. Click on browse button to choose the path.\n"
            "2. Choose the path where TallyPrime is located and select the folder.\n"
            "3. Validate the folder to start the diagnostics."
        )
        instructions_label.setStyleSheet("color: #e0e0e0; font-size: 12px; padding: 5px; background-color: rgba(0,0,0,0.2); border-radius: 5px;")
        instructions_label.setWordWrap(True)
        instructions_label.setContentsMargins(8, 8, 8, 8)
        self.main_layout.addWidget(instructions_label)

        # Initialize the validated_tally_path to None
        self.validated_tally_path = None

        # Try to load the saved Tally Prime path from UserConfig.json, but don't rely on it
        try:
            from CustomHelper import CResoucedataHelper
            user_config = CResoucedataHelper.MSGetUserConfig() or {}
            saved_path = user_config.get("TallyPrime", {}).get("InstallPath", "")
            if saved_path and os.path.isdir(saved_path) and os.path.isfile(os.path.join(saved_path, "tally.exe")):
                self.tally_location_input.setText(saved_path)
                # Don't set validated_tally_path here - we'll validate it when the user clicks the button
                CLogger.MCWriteLog("info", f"Loaded saved TallyPrime installation path: {saved_path}")
            else:
                # Try to find TallyPrime in common locations
                common_paths = [
                    r"C:\Program Files\TallyPrime",
                    r"C:\Program Files (x86)\TallyPrime",
                    r"D:\TallyPrime"
                ]
                for path in common_paths:
                    if os.path.isdir(path) and os.path.isfile(os.path.join(path, "tally.exe")):
                        self.tally_location_input.setText(path)
                        CLogger.MCWriteLog("info", f"Found TallyPrime installation at common location: {path}")
                        break
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to load saved TallyPrime installation path: {e}")

        # Header label
        self.current_check_label = QLabel("Starting diagnostics...")
        self.current_check_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.main_layout.addWidget(self.current_check_label)

        # Table for diagnostic checks
        self.check_names = [
            "🌐 Internet Connection Status",
            "🚀 Real-Time Internet Speed Test",
            "🧠 Memory (RAM) Health Check",
            "⚙️ Processor (CPU) Performance Monitor",
            "💾 Storage & Disk Space Analysis",
            "📡 Static IP Configuration Status",
            "🔄 Dynamic IP Status & Refresh",
            "🖥️ AccuVelocity Server Connectivity",
            "🔍 TallyPrime Version Verification",
            "🛠️ TallyPrime Configuration Audit",
            "🔗 TallyPrime Server Accessibility",
            "🧾 Tally License File Validation",
            "📁 Key Files & Folders Integrity Check"
        ]
        self.table = QTableWidget(len(self.check_names), 3)
        self.table.setHorizontalHeaderLabels(["AccuVelocity Check", "Status", "AccuVelocity Action Comments"])
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Fixed)
        self.table.setColumnWidth(1, 100)
        self.table.setColumnWidth(2, 300)
        
        # Fix the corner button styling and add modern scrollbar
        self.table.setStyleSheet("""
            QTableWidget {
                background: rgba(255, 255, 255, 0.9);
                alternate-background-color: #f0f0f0;
                selection-background-color: #d0d0d0;
                gridline-color: #cccccc;
                border-radius: 10px;
                border: 1px solid #4a6491;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 6px;
                border: 1px solid #34495e;
                font-weight: bold;
            }
            QTableCornerButton::section {
                background-color: #2c3e50;
                border: 1px solid #34495e;
            }
            
            /* Modern Scrollbar Styling */
            QScrollBar:vertical {
                border: none;
                background: rgba(0, 0, 0, 0.1);
                width: 10px;
                margin: 0px;
                border-radius: 5px;
            }
            QScrollBar::handle:vertical {
                background: #2196F3;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::handle:vertical:hover {
                background: #1976D2;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
            
            /* Horizontal scrollbar - similar styling */
            QScrollBar:horizontal {
                border: none;
                background: rgba(0, 0, 0, 0.1);
                height: 10px;
                margin: 0px;
                border-radius: 5px;
            }
            QScrollBar::handle:horizontal {
                background: #2196F3;
                min-width: 20px;
                border-radius: 5px;
            }
            QScrollBar::handle:horizontal:hover {
                background: #1976D2;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {
                background: none;
            }
        """)

        tbl_font = self.table.font()
        tbl_font.setPointSize(tbl_font.pointSize() + 2)
        self.table.setFont(tbl_font)

        # Enable vertical scrolling for more than 10 rows
        self.table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        max_visible_rows = 10
        row_height = self.table.verticalHeader().defaultSectionSize()
        header_height = self.table.horizontalHeader().height()
        self.table.setMaximumHeight(header_height + row_height * max_visible_rows)

        for i, name in enumerate(self.check_names):
            item_name = QTableWidgetItem(name)
            item_name.setFlags(item_name.flags() & ~Qt.ItemIsEditable)
            item_name.setForeground(QColor("#000000"))
            self.table.setItem(i, 0, item_name)

            item_status = QTableWidgetItem("⏳")
            item_status.setFlags(item_status.flags() & ~Qt.ItemIsEditable)
            item_status.setForeground(QColor("#6c757d"))
            self.table.setItem(i, 1, item_status)

            item_comments = QTableWidgetItem("-")
            item_comments.setFlags(item_comments.flags() & ~Qt.ItemIsEditable)
            item_comments.setForeground(QColor("#6c757d"))
            self.table.setItem(i, 2, item_comments)

            bg_color = "#f9f9f9" if i % 2 == 0 else "#ffffff"
            for col in range(3):
                self.table.item(i, col).setBackground(QColor(bg_color))

        self.main_layout.addWidget(self.table)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.main_layout.addWidget(self.progress_bar)

        # Add some spacing before the tagline
        self.main_layout.addSpacing(15)

        # Tagline label with improved styling
        self.tagline_label = QLabel("Powered by AccuVelocity AI")
        self.tagline_label.setAlignment(Qt.AlignCenter)
        self.tagline_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.tagline_label.setStyleSheet("""
            color: #BDC3C7; 
            padding: 10px; 
            margin-top: 10px;
            letter-spacing: 1px;
        """)
        self.main_layout.addWidget(self.tagline_label)

        # Layout margins and spacing
        self.main_layout.setContentsMargins(15, 15, 15, 15)
        self.main_layout.setSpacing(12)

        # Initialize diagnostics
        self.diag = CRunDiagnostics()

        # Store the validated Tally Prime path
        self.validated_tally_path = None

        # Start the diagnostics thread when the window is shown
        # We'll start it after validation or when the user clicks a button
        self.thread = None

        # Enable comment dialog on double-click
        self.table.cellDoubleClicked.connect(self.show_comment_window)

    def browse_tally_location(self):
        """Open a file dialog to browse for the Tally Prime installation directory"""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select TallyPrime Installation Directory",
            "C:\\Program Files"  # Default starting directory
        )
        if directory:
            self.tally_location_input.setText(directory)

    def validate_tally_location(self):
        """Validate the entered Tally Prime installation path"""
        path = self.tally_location_input.text().strip()

        if not path:
            QMessageBox.warning(
                self,
                "Validation Error",
                "Please enter a TallyPrime installation path."
            )
            return

        # Check if the directory exists
        if not os.path.isdir(path):
            QMessageBox.warning(
                self,
                "Validation Error",
                f"The directory '{path}' does not exist."
            )
            return

        # Check if tally.exe exists in the directory
        tally_exe_path = os.path.join(path, "tally.exe")
        if not os.path.isfile(tally_exe_path):
            QMessageBox.warning(
                self,
                "Validation Error",
                f"Could not find tally.exe in '{path}'.\nPlease select a valid TallyPrime installation directory."
            )
            return

        # Path is valid
        self.validated_tally_path = path
        QMessageBox.information(
            self,
            "Validation Successful",
            f"TallyPrime installation found at '{path}'.\nDiagnostics will use this path."
        )

        # Save the path to UserConfig.json for future use
        try:
            from CustomHelper import CResoucedataHelper
            user_config_path = CResoucedataHelper.MSGetUserConfigLocation()
            user_config = CResoucedataHelper.MSGetUserConfig() or {}

            # Update the config with the Tally Prime path
            if "TallyPrime" not in user_config:
                user_config["TallyPrime"] = {}
            user_config["TallyPrime"]["InstallPath"] = path

            # Save the updated config
            with open(user_config_path, 'w') as f:
                import json
                json.dump(user_config, f, indent=4)

            CLogger.MCWriteLog("info", f"Saved TallyPrime installation path to UserConfig: {path}")
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to save TallyPrime installation path: {e}")

        # Start the diagnostics thread with the validated path
        self.start_diagnostics()

    def start_diagnostics(self):
        """Start the diagnostics thread with the validated Tally Prime path"""
        # If there's a path entered but not validated, try to validate it first
        if self.tally_location_input.text().strip() and not self.validated_tally_path:
            # Attempt to validate the entered path
            path = self.tally_location_input.text().strip()
            if os.path.isdir(path) and os.path.isfile(os.path.join(path, "tally.exe")):
                # Path is valid, set it as validated
                self.validated_tally_path = path
                CLogger.MCWriteLog("info", f"Auto-validated TallyPrime path: {path}")
            else:
                # Path is not valid, ask user what to do
                result = QMessageBox.question(
                    self,
                    "Validate Path",
                    f"The TallyPrime path '{path}' appears to be invalid. Would you like to:\n\n"
                    "- Yes: Validate this path (if you believe it's correct)\n"
                    "- No: Continue without a TallyPrime path\n"
                    "- Cancel: Browse for a different path",
                    QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                    QMessageBox.Yes
                )
                if result == QMessageBox.Yes:
                    self.validate_tally_location()
                    return
                elif result == QMessageBox.Cancel:
                    self.browse_tally_location()
                    return
                # If No, continue without a path

        # If no path is provided at all, ask if user wants to specify one
        elif not self.tally_location_input.text().strip() and not self.validated_tally_path:
            result = QMessageBox.question(
                self,
                "TallyPrime Path",
                "No TallyPrime installation path has been provided. The diagnostics will try to find TallyPrime automatically.\n\n"
                "Would you like to specify a path now?\n\n"
                "Note: Some diagnostic checks require a valid TallyPrime installation path.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            if result == QMessageBox.Yes:
                self.browse_tally_location()
                return

        # Reset the table status indicators
        for i in range(len(self.check_names)):
            item = self.table.item(i, 1)
            item.setText("⏳")
            item.setForeground(QColor("#6c757d"))
            item.setBackground(QColor("#f9f9f9" if i % 2 == 0 else "#ffffff"))

            item_comments = self.table.item(i, 2)
            item_comments.setText("-")
            item_comments.setForeground(QColor("#6c757d"))

        # Reset progress bar
        self.progress_bar.setValue(0)

        # Update the current check label
        self.current_check_label.setText("Starting diagnostics...")

        # Disable buttons while diagnostics are running
        self.start_button.setEnabled(False)
        self.browse_button.setEnabled(False)
        self.validate_button.setEnabled(False)
        self.tally_location_input.setEnabled(False)

        # Remove any existing download report button
        if hasattr(self, 'download_report_button') and self.download_report_button:
            try:
                self.download_report_button.setParent(None)
                self.download_report_button.deleteLater()
                self.download_report_button = None
            except:
                pass

        # Initialize and start the thread
        self.thread = DiagnosticsThread(self.diag, self.validated_tally_path)
        self.thread.update_status.connect(self.update_check_status)
        self.thread.current_check.connect(self.update_current_check)
        self.thread.finished.connect(self.diagnostics_complete)
        self.thread.start()

    def update_current_check(self, check_name):
        self.current_check_label.setText(f"Running: {check_name}")

    def update_check_status(self, index, success, comment):
        item = self.table.item(index, 1)
        item_comments = self.table.item(index, 2)

        # Set status icon and colors
        if success:
            item.setText("✅")
            item.setForeground(QColor("#155724"))  # Dark green text
            item.setBackground(QColor("#d4edda"))  # Light green background

            # If no comment provided for success, use a default message
            if not comment or comment == "":
                check_name = self.check_names[index].split(" ", 1)[1] if " " in self.check_names[index] else self.check_names[index]
                comment = f"✓ {check_name} check completed successfully."

            # Set comment text with success styling
            item_comments.setText(comment)
            item_comments.setForeground(QColor("#155724"))  # Dark green text
            item_comments.setBackground(QColor("#d4edda"))  # Light green background
        else:
            item.setText("❌")
            item.setForeground(QColor("#721c24"))  # Dark red text
            item.setBackground(QColor("#f8d7da"))  # Light red background

            # Ensure we have a comment for failure
            if not comment or comment == "":
                check_name = self.check_names[index].split(" ", 1)[1] if " " in self.check_names[index] else self.check_names[index]
                comment = f"Error: {check_name} check failed. Please review the diagnostic details."

            # Set comment text with failure styling
            item_comments.setText(comment)
            item_comments.setForeground(QColor("#721c24"))  # Dark red text
            item_comments.setBackground(QColor("#f8d7da"))  # Light red background

        # Update progress bar
        progress = int((index + 1) / len(self.check_names) * 100)
        self.progress_bar.setValue(progress)

    def diagnostics_complete(self):
        self.current_check_label.setText("Diagnostics Complete")

        # Re-enable buttons
        self.browse_button.setEnabled(True)
        self.validate_button.setEnabled(True)
        self.tally_location_input.setEnabled(True)

        # Change Start Diagnostics button to Restart Diagnostics
        self.start_button.setText("Restart Diagnostics")
        self.start_button.setEnabled(True)

        # Update button style to indicate it's a restart
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;  /* Blue */
                font-size: 14px;
                padding: 5px 15px;
                color: white;
            }
            QPushButton:hover {
                background-color: #0b7dda;
            }
            QPushButton:pressed {
                background-color: #0a6fc2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        # Add Download Report button
        self.add_download_report_button()

    def add_download_report_button(self):
        """Add a Download Report button after diagnostics are complete"""
        # Remove any existing download report button and layout
        if hasattr(self, 'download_report_button') and self.download_report_button:
            try:
                self.download_report_button.setParent(None)
                self.download_report_button.deleteLater()
            except:
                pass

        if hasattr(self, 'report_button_layout') and self.report_button_layout:
            try:
                # Remove all widgets from the layout
                while self.report_button_layout.count():
                    item = self.report_button_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
            except:
                pass
        else:
            # Create a new button layout
            self.report_button_layout = QHBoxLayout()
            self.main_layout.addLayout(self.report_button_layout)

        # Clear and rebuild the layout
        self.report_button_layout.addStretch()

        # Create the Download Report button
        self.download_report_button = QPushButton("Download Detailed Report (PDF)")
        self.download_report_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;  /* Green */
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 10px;
                border: none;
                min-width: 250px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.download_report_button.clicked.connect(self.generate_report)
        self.report_button_layout.addWidget(self.download_report_button)

    def generate_report(self):
        """Generate a detailed PDF report with all diagnostic information"""
        try:
            # Disable buttons while generating report
            if hasattr(self, 'download_report_button') and self.download_report_button:
                self.download_report_button.setEnabled(False)
                self.download_report_button.setText("Generating Report...")

            self.start_button.setEnabled(False)
            self.browse_button.setEnabled(False)
            self.validate_button.setEnabled(False)

            # Create a default filename with timestamp
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"AccuVelocity_Diagnostic_Report_{timestamp}.pdf"

            # Ask user where to save the report
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save Diagnostic Report",
                os.path.join(os.path.expanduser("~"), "Documents", default_filename),
                "PDF Files (*.pdf)"
            )

            if not file_path:
                # Re-enable buttons if user cancels
                if hasattr(self, 'download_report_button') and self.download_report_button:
                    self.download_report_button.setEnabled(True)
                    self.download_report_button.setText("Download Detailed Report (PDF)")

                self.start_button.setEnabled(True)
                self.browse_button.setEnabled(True)
                self.validate_button.setEnabled(True)
                return  # User cancelled

            # Create a progress dialog
            progress_dialog = QMessageBox(self)
            progress_dialog.setWindowTitle("Generating Report")
            progress_dialog.setText("Generating detailed diagnostic report...\nThis may take a few moments.")
            progress_dialog.setStandardButtons(QMessageBox.NoButton)
            progress_dialog.setModal(True)  # Make it modal to block user interaction

            # Process events to ensure the dialog is shown immediately
            progress_dialog.show()
            QApplication.processEvents()

            # Collect diagnostic results
            check_results = []
            for i in range(len(self.check_names)):
                check_name = self.check_names[i]
                status_item = self.table.item(i, 1)
                comment_item = self.table.item(i, 2)

                is_success = status_item.text() == "✅"
                comment = comment_item.text()

                check_results.append((check_name, is_success, comment))

            # Generate the report
            report_generator = AccuVelocityReportGenerator(self.diag, check_results)
            success = report_generator.generate_report(file_path)

            # Ensure the progress dialog is closed
            progress_dialog.close()
            progress_dialog.deleteLater()  # Schedule the dialog for deletion
            QApplication.processEvents()  # Process events to ensure UI updates

            # Re-enable buttons
            self.start_button.setEnabled(True)
            self.browse_button.setEnabled(True)
            self.validate_button.setEnabled(True)

            if hasattr(self, 'download_report_button') and self.download_report_button:
                self.download_report_button.setEnabled(True)
                self.download_report_button.setText("Download Detailed Report (PDF)")

            if success:
                # Combine the success message with the open prompt to reduce the number of dialogs
                result = QMessageBox.question(
                    self,
                    "Report Generated Successfully",
                    f"Diagnostic report has been successfully generated and saved to:\n\n{file_path}\n\nWould you like to open the report now?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if result == QMessageBox.Yes:
                    # Open the PDF with the default application
                    if sys.platform == 'win32':
                        os.startfile(file_path)
                    # Note: The following code is included for completeness but won't run on Windows
                    # elif sys.platform == 'darwin':  # macOS
                    #     os.system(f'open "{file_path}"')
                    # else:  # Linux
                    #     os.system(f'xdg-open "{file_path}"')
            else:
                QMessageBox.warning(
                    self,
                    "Report Generation Failed",
                    "Failed to generate the diagnostic report.\n\nPossible reasons:\n- Insufficient disk space\n- Permission issues\n- File is in use by another application\n\nPlease try again or choose a different location."
                )
        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to generate diagnostic report: {e}")

            # Make sure any progress dialog is closed in case of exception
            try:
                if 'progress_dialog' in locals() and progress_dialog.isVisible():
                    progress_dialog.close()
                    progress_dialog.deleteLater()
                    QApplication.processEvents()
            except:
                pass

            # Re-enable buttons
            try:
                self.start_button.setEnabled(True)
                self.browse_button.setEnabled(True)
                self.validate_button.setEnabled(True)

                if hasattr(self, 'download_report_button') and self.download_report_button:
                    self.download_report_button.setEnabled(True)
                    self.download_report_button.setText("Download Detailed Report (PDF)")
            except:
                pass

            # Show a user-friendly error message
            QMessageBox.critical(
                self,
                "Report Generation Error",
                f"An unexpected error occurred while generating the report:\n\n{str(e)}\n\nPlease try again or contact support if the issue persists."
            )

    def show_comment_window(self, row, column):
        if column == 2:
            text = self.table.item(row, 2).text()
            # Check if this is a success or failure based on the status column
            status_item = self.table.item(row, 1)
            is_success = status_item.text() == "✅"

            # Create the comment window with the appropriate status
            dlg = AccuVelocityActionCommentWindow(text, self, is_success)
            dlg.exec_()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = CDiagnosticsWindow()
    win.show()
    sys.exit(app.exec_())
