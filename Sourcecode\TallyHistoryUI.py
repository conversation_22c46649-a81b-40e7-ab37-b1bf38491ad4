import sys
import os
import uuid
import json
from datetime import datetime
import zipfile
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget,
    QTreeWidgetItem, QPushButton, QComboBox, QLineEdit, QDateEdit, QLabel,
    QMessageBox, QFileDialog, QStatusBar, QSpinBox, QTabWidget, QProgressBar
)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette
import requests
from pathlib import Path
from EncryptResourceConfig import CReadDeveloperConfig
from CustomHelper import CLicenseHelper, CTallyExportHelper, CResoucedataHelper, CGeneralHelper, CArgumentParser
from FileProcessor import CFileProcessor 
import traceback
from CustomLogger import CLogger
import httpx

class VoucherType:
    PV_WITH_INVENTORY = "PV_WITH_INVENTORY"
    PV_WITHOUT_INVENTORY = "PV_WITHOUT_INVENTORY"
    DELIVERY_NOTE = "DELIVERY_NOTE"
    JOURNAL_VOUCHER = "JOURNAL_VOUCHER"
    BANK_STATEMENT = "BANK_STATEMENT"
    RECEIPT_NOTE = "RECEIPT_NOTE"
    PURCHASE_ORDER = "PURCHASE_ORDER"

VOUCHER_TYPES = [
    VoucherType.PV_WITH_INVENTORY,
    VoucherType.PV_WITHOUT_INVENTORY,
    VoucherType.DELIVERY_NOTE,
    VoucherType.JOURNAL_VOUCHER,
    VoucherType.BANK_STATEMENT,
    VoucherType.RECEIPT_NOTE,
    VoucherType.PURCHASE_ORDER
]

class AVXMLGeneratedStatus:
    Success = "Success"
    Skipped = "Skipped"
    PartialSuccess = "PartialSuccess"
    Duplicate = "Duplicate"
    NOT_APPLICABLE = "NOT_APPLICABLE"
    ValidationError = "ValidationError"

STATUS_TYPES = [
    "All",
    AVXMLGeneratedStatus.Success,
    AVXMLGeneratedStatus.Skipped,
    AVXMLGeneratedStatus.PartialSuccess,
    AVXMLGeneratedStatus.Duplicate,
    AVXMLGeneratedStatus.NOT_APPLICABLE,
    AVXMLGeneratedStatus.ValidationError
]

class TallyXMLImportStatus:
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

TALLY_STATUS_TYPES = [
    "All",
    TallyXMLImportStatus.SUCCESS,
    TallyXMLImportStatus.FAILED
]

PAGE_SIZES = [10, 20, 30, 40, 50, 100]

def MSProcessXMLFiles(extracted_files, strToken, strReqID = None):
        """
        Input:
            extracted_files: list
                List of extracted XML file paths to be processed.
            strToken: str
                Authorization token for making API calls to the AHM server.

        Output:
            dict: API response from the AHM server after sending the processed Tally response.

        Purpose:
            To process received XML files by sending them to the Tally server, generating response XML files,
            and then packaging and sending the response XML files to the AHM server.
        """
        try:
            dictUserConfig = CResoucedataHelper.MSGetUserConfig()
            dictDeveloperConfig = CResoucedataHelper.MSGetDeveloperConfig()
            dictAccuVelocityConfig =  CResoucedataHelper.MSGetAccuvelocityConfig()
            lsalternative_bases = dictAccuVelocityConfig.get("apiEndpoints")
            strTallyServerUrl = dictUserConfig.get("ExportRequest").get("ReqUrl")
            
            current_date = datetime.now().strftime("%Y%m%d_%H%M%S") if strReqID is None else strReqID 
            strTallyResponseXMLDir = os.path.join(CGeneralHelper.MSGetResourceDirectory(),"Manual_Import",current_date)
            
            lsReceivedXMLFiles = []
            # Filter XML files and count them
            xml_files = [f for f in extracted_files if f.endswith('.xml')]
            total_files = len(xml_files)
            processed_files = 0

            # Process each XML file
            os.makedirs(strTallyResponseXMLDir, exist_ok=True)
            for xml_file_path in xml_files:
                response_path = os.path.join(strTallyResponseXMLDir, f"TallyXMLResponse-{os.path.basename(xml_file_path)}")
                # Create an empty response file
                with open(response_path, 'w') as file:
                    file.write("")
                lsReceivedXMLFiles.append(response_path)
                
                # Process file with Tally server
                strExportedAbstractDataComment = CTallyExportHelper.MSExportTallyData(
                    strRequestXMLPath=xml_file_path,
                    strResponseXMLPath=response_path,
                    url=strTallyServerUrl
                )

                
                # Increment processed files
                processed_files += 1

            # API call to AHM server
            strClientRespUrl = dictDeveloperConfig.get("ExportRequest").get("APIEndPoints").get("ClientResp")
            try:
                strClientRespUrl=str(CArgumentParser.MSGet_working_endpoint(strClientRespUrl,lsalternative_bases))
            except Exception as e:
                print("Error Occur in Client Req URL")
            current_mac = hex(uuid.getnode()).replace("0x", "").upper()
            dictAccuVelocityConfig["USER_UUID"] = str(current_mac)
            dictAccuVelocityConfig["ClientImportedXMLType"] = "Manual_Import"

            dictAccuVelocityConfig["ClientReqImportedAt"] = CGeneralHelper.MSGetCurrentFormatTimeStamp()
            CTallyExportHelper.MSSendTallyImportedXMLResToServer(dictInstalledExeDetail = dictAccuVelocityConfig, strTallyResponseXMLDir = strTallyResponseXMLDir, lsXMLFiles = lsReceivedXMLFiles, strToken = strToken, strURL = strClientRespUrl)
            
        except Exception as e:
            print("Error Occur in Importing XML Files" , traceback.format_exc())


class DataLoaderThread(QThread):
    data_loaded = pyqtSignal(str, dict)

    def __init__(self, url, params, filters, voucher_type, parent=None,strUserToken:str = None):
        super().__init__(parent)
        self.url = url
        self.params = params
        self.voucher_type = voucher_type
        self.strUserToken = strUserToken
        self.filters = filters

    def run(self):
        headers = {
            "Authorization": f"Bearer {self.strUserToken}",
            "Content-Type": "application/json"
        }
        try:
             # Will wait for 10 minutes for server response
            with httpx.Client(timeout=600) as client:
                response = client.post(
                    self.url,
                    headers=headers,
                    params=self.params,
                    json=self.filters
                )

            if response.status_code == 403:
                raise Exception("This feature is not available for your account.")
            if response.status_code == 500:
                raise Exception("Internal server error. Please check logs.")
            response.raise_for_status()
            data = response.json()
            self.data_loaded.emit(self.voucher_type, data)
        except requests.exceptions.RequestException as e:
            print("------------------------------------------", traceback.print_exc())
            self.data_loaded.emit(self.voucher_type, {"error": str(e)})

class DocumentHistoryApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Document History")
        self.setGeometry(100, 100, 1300, 800)
        CLogger.MCSetupLogging()
        dictLicenseData = CLicenseHelper.MSVerifyLicense()
        strUserToken = dictLicenseData["Token"]
        self.user_id = strUserToken
        self.current_page = {}
        self.page_size = 10
        self.total_pages = {}
        self.all_data = {}
        self.trees = {}
        self.data_load_status = {}
        self.progress_bars = {}
        for voucher_type in VOUCHER_TYPES:
            self.current_page[voucher_type] = 1
            self.total_pages[voucher_type] = 1
            self.all_data[voucher_type] = []
            self.data_load_status[voucher_type] = False
        self.setWindowFlags(self.windowFlags() | Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)
        self.init_ui()
        self.load_data_for_all_tabs()

    def set_styles(self):
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1E2A44, stop:1 #2A3A55);
                border-radius: 10px;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
            }
            QLineEdit {
                background: #ffffff;
                border: 1px solid #4a6491;
                border-radius: 10px;
                padding: 5px;
                font-size: 14px;
                color: #333333;
                min-width: 150px;
            }
            QComboBox {
                background: #ffffff;
                border: 1px solid #4a6491;
                border-radius: 10px;
                padding: 5px;
                font-size: 14px;
                color: #333333;
                min-width: 150px;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 20px;
                border-left: 1px solid #4a6491;
                border-top-right-radius: 10px;
                border-bottom-right-radius: 10px;
                background-color: #ffffff;
            }
            QComboBox::down-arrow {
                image: url(:/qt-project.org/styles/commonstyle/downarrow.png);
                width: 12px;
                height: 12px;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                background: #ffffff;
                border: 1px solid #4a6491;
                selection-background-color: #d0d0d0;
            }
            QDateEdit {
                background: #ffffff;
                border: 1px solid #4a6491;
                border-radius: 10px;
                padding: 5px;
                font-size: 14px;
                color: #333333;
                min-width: 150px;
            }
            QSpinBox {
                background: #ffffff;
                border: 1px solid #4a6491;
                border-radius: 10px;
                padding: 5px;
                font-size: 14px;
                color: #333333;
                min-width: 100px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border-radius: 10px;
                padding: 5px 10px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background-color: #1E88E5;
                border: 1px solid #0d47a1;
            }
            QPushButton:pressed {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
                border: 1px solid #999999;
            }
            QPushButton#download_btn {
                background-color: #4CAF50;
            }
            QPushButton#download_btn:hover {
                background-color: #45a049;
            }
            QPushButton#download_btn:pressed {
                background-color: #3d8b40;
            }
            QPushButton#download_btn:disabled {
                background-color: #cccccc;
                color: #666666;
                border: 1px solid #999999;
            }
            QTreeWidget {
                background: rgba(255, 255, 255, 0.95);
                alternate-background-color: #F5F7FA;
                selection-background-color: #D6E4FF;
                border-radius: 10px;
                border: 1px solid #4a6491;
                show-decoration-selected: 1;
            }
            QTreeWidget::item {
                border-right: 1px solid #4a6491;
                border-bottom: 1px solid #4a6491;
                padding: 5px;
                background-color: #FFFFFF;
                color: #333333;
            }
            QTreeWidget::item:alternate {
                background-color: #F5F7FA;
            }
            QTreeWidget::item:hover {
                background-color: #E8ECEF;
            }
            QTreeWidget::item:selected {
                background: #D6E4FF;
                color: #222222;
            }
            QTreeWidget::branch {
                border-right: 1px solid #4a6491;
            }
            QHeaderView::section {
                background: #E0E0E0;
                color: #333333;
                border: 1px solid #4a6491;
                padding: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QStatusBar {
                color: #ffffff;
                font-size: 14px;
            }
            QTabWidget::pane {
                border: 1px solid #4a6491;
                background: #2A3A55;
                border-radius: 10px;
            }
            QTabBar::tab {
                background: #2196F3;
                color: white;
                padding: 8px 16px;
                margin: 2px;
                border-radius: 5px;
                min-width: 150px;
            }
            QTabBar::tab:selected {
                background: #4CAF50;
                color: white;
            }
            QTabBar::tab:hover {
                background: #1E88E5;
            }
            QMessageBox {
                background-color: #ffffff;
                color: #000000 !important;
                font-size: 16px;
                font-family: Arial, sans-serif;
            }
            QMessageBox QLabel {
                color: #000000;
                font-size: 16px;
            }
            QMessageBox QMessageBoxIcon {
                width: 32px;
                height: 32px;
            }
            QProgressBar {
                border: 1px solid #4a6491;
                border-radius: 5px;
                text-align: center;
                color: #333333;
                font-size: 14px;
                background-color: #ffffff;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 5px;
            }
        """)

    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        filter_layout = QHBoxLayout()
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        filter_layout.addWidget(QLabel("Start Date:"))
        filter_layout.addWidget(self

.start_date)
        filter_layout.addSpacing(20)

        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())
        filter_layout.addWidget(QLabel("End Date:"))
        filter_layout.addWidget(self.end_date)
        filter_layout.addSpacing(20)

        self.status = QComboBox()
        self.status.addItems(STATUS_TYPES)
        self.status.setCurrentText("All")
        filter_layout.addWidget(QLabel("XML Status:"))
        filter_layout.addWidget(self.status)
        filter_layout.addSpacing(20)

        self.tally_status = QComboBox()
        self.tally_status.addItems(TALLY_STATUS_TYPES)
        self.tally_status.setCurrentText("All")
        filter_layout.addWidget(QLabel("Tally Status:"))
        filter_layout.addWidget(self.tally_status)
        filter_layout.addSpacing(20)
        
        self.document_name = QLineEdit()
        self.document_name.setPlaceholderText("Enter document name...")
        filter_layout.addWidget(QLabel("Document Name:"))
        filter_layout.addWidget(self.document_name)
        filter_layout.addSpacing(20)

        apply_filter_btn = QPushButton("Apply Filters")
        apply_filter_btn.clicked.connect(self.load_data_for_all_tabs)
        filter_layout.addWidget(apply_filter_btn)
        filter_layout.addStretch()
        main_layout.addLayout(filter_layout)
        
        page_layout = QHBoxLayout()
        page_layout.addWidget(QLabel("Records per Page:"))
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems([str(size) for size in PAGE_SIZES])
        self.page_size_combo.setCurrentText(str(self.page_size))
        self.page_size_combo.currentTextChanged.connect(self.change_page_size)
        page_layout.addWidget(self.page_size_combo)
        page_layout.addStretch()
        main_layout.addLayout(page_layout)

        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        for voucher_type in VOUCHER_TYPES:
            tab = QWidget()
            tab_layout = QVBoxLayout(tab)
            
            progress_bar = QProgressBar()
            progress_bar.setRange(0, 0)
            progress_bar.setVisible(False)
            progress_bar.setTextVisible(True)
            progress_bar.setFormat("Loading data for %v...")
            self.progress_bars[voucher_type] = progress_bar
            tab_layout.addWidget(progress_bar)
            
            tree = QTreeWidget()
            tree.setHeaderLabels([
                "Select", "ID", "Voucher Type", "Accuvelocity Status", "Tally Import Status",
                "Document Name", "Document Type", "Generated At",
                "Customer Name", "User Name", "Has XML"
            ])
            tree.setColumnWidth(0, 50)
            tree.setColumnWidth(1, 80)
            tree.setColumnWidth(2, 120)
            tree.setColumnWidth(3, 100)
            tree.setColumnWidth(4, 100)
            tree.setColumnWidth(5, 200)
            tree.setColumnWidth(6, 100)
            tree.setColumnWidth(7, 150)
            tree.setColumnWidth(8, 150)
            tree.setColumnWidth(9, 150)
            tree.setColumnWidth(10, 80)
            # tree.setColumnWidth(11, 150)
            # tree.setColumnWidth(12, 150)
            # tree.setColumnWidth(13, 150)
            # tree.setColumnWidth(14, 150)
            # tree.setColumnWidth(15, 150)
            # tree.setColumnWidth(16, 150)
            tree.itemChanged.connect(lambda item, col, vt=voucher_type: self.handle_item_changed(item, col, vt))
            tab_layout.addWidget(tree)
            self.trees[voucher_type] = tree
            self.tab_widget.addTab(tab, voucher_type)

        pagination_layout = QHBoxLayout()
        pagination_layout.addSpacing(20)

        self.prev_btn = QPushButton("Previous")
        self.prev_btn.clicked.connect(self.prev_page)
        pagination_layout.addWidget(self.prev_btn)
        pagination_layout.addSpacing(10)

        pagination_layout.addWidget(QLabel("Page:"))
        self.page_input = QSpinBox()
        self.page_input.setMinimum(1)
        self.page_input.setValue(1)
        self.page_input.valueChanged.connect(self.goto_page)
        pagination_layout.addWidget(self.page_input)
        pagination_layout.addSpacing(20)

        self.page_label = QLabel("of 1")
        pagination_layout.addWidget(self.page_label)
        pagination_layout.addSpacing(20)

        self.next_btn = QPushButton("Next")
        self.next_btn.clicked.connect(self.next_page)
        pagination_layout.addWidget(self.next_btn)
        pagination_layout.addStretch()
        main_layout.addLayout(pagination_layout)

        main_layout.addSpacing(20)

        download_layout = QHBoxLayout()
        download_layout.addStretch()
        self.download_selected_btn = QPushButton("Import XML(s) to Tally")
        self.download_selected_btn.setObjectName("download_btn")
        self.download_selected_btn.clicked.connect(self.download_selected)
        download_layout.addWidget(self.download_selected_btn)
        download_layout.addStretch()
        main_layout.addLayout(download_layout)

        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

        self.set_styles()
        self.tab_widget.currentChanged.connect(self.update_pagination_controls)
        self.dictDeveloperConfig = CResoucedataHelper.MSGetDeveloperConfig()
        self.ActivityURL = self.dictDeveloperConfig.get("ExportRequest").get("APIEndPoints").get("ActivityURL")
        self.SingleXMLDownloadURL = self.dictDeveloperConfig.get("ExportRequest").get("APIEndPoints").get("SingleXMLDownloadURL")
        self.MultipleXMLDownloadURL = self.dictDeveloperConfig.get("ExportRequest").get("APIEndPoints").get("MultipleXMLDownloadURL")
        
    def update_pagination_buttons(self):
        current_voucher_type = self.tab_widget.tabText(self.tab_widget.currentIndex())
        self.prev_btn.setEnabled(self.current_page[current_voucher_type] > 1)
        self.next_btn.setEnabled(self.current_page[current_voucher_type] < self.total_pages[current_voucher_type])

    def update_pagination_controls(self):
        current_voucher_type = self.tab_widget.tabText(self.tab_widget.currentIndex())
        self.page_input.setValue(self.current_page[current_voucher_type])
        self.page_label.setText(f"of {self.total_pages[current_voucher_type]}")
        self.update_pagination_buttons()

    def load_data_for_all_tabs(self):
        for voucher_type in VOUCHER_TYPES:
            self.data_load_status[voucher_type] = False
            self.all_data[voucher_type] = []
            self.current_page[voucher_type] = 1
            self.total_pages[voucher_type] = 1
            self.trees[voucher_type].clear()
            self.progress_bars[voucher_type].setVisible(True)
        self.pending_loads = len(VOUCHER_TYPES)
        for voucher_type in VOUCHER_TYPES:
            self.load_data(voucher_type)
        self.update_pagination_controls()

    def load_data(self, voucher_type):
        self.progress_bars[voucher_type].setVisible(True)
        filters = {}
        if self.start_date.date().toPyDate() != QDate.currentDate().addDays(-30).toPyDate():
            filters["start_date"] = self.start_date.date().toString("yyyy-MM-dd")
        if self.end_date.date().toPyDate() != QDate.currentDate().toPyDate():
            filters["end_date"] = self.end_date.date().toString("yyyy-MM-dd")
        if self.status.currentText() != "All":
            filters["status"] = self.status.currentText()
        if self.tally_status.currentText() != "All":
            filters["tally_status"] = self.tally_status.currentText()
        if self.document_name.text():
            filters["document_name"] = self.document_name.text()
        if voucher_type != "All":
            filters["voucher_type"] = voucher_type


        thread = DataLoaderThread(
        
            self.ActivityURL,
            {"page": self.current_page[voucher_type], "page_size": self.page_size},
            filters,
            voucher_type,
            self,
            self.user_id
        )
        thread.data_loaded.connect(lambda vt, data: self.on_data_loaded(vt, data))
        thread.start()



    def on_data_loaded(self, voucher_type, data):
        tree = self.trees[voucher_type]
        self.progress_bars[voucher_type].setVisible(False)
        if "error" in data:
            QMessageBox.critical(self, "Error", f"Failed to load data for {voucher_type}: {data['error']}")
            self.status_bar.showMessage("Error loading data")
            self.data_load_status[voucher_type] = True
            self.all_data[voucher_type] = []
            self.total_pages[voucher_type] = 1
        else:
            self.total_pages[voucher_type] = data["total_pages"]
            self.all_data[voucher_type] = data["documents"][:self.page_size]
            tree.clear()
            if self.all_data[voucher_type]:
                for doc in self.all_data[voucher_type]:
                    parent = QTreeWidgetItem(tree)
                    parent.setFlags(parent.flags() | Qt.ItemIsUserCheckable)
                    parent.setCheckState(0, Qt.Unchecked)
                    parent.setText(1, str(doc["ID"]))
                    parent.setText(2, doc["strVoucherType"] or "")
                    parent.setText(3, doc["AVDocumentPtocessedStatus"] or "")
                    parent.setText(4, doc["TallyXMLImportStatus"] or "")
                    parent.setText(5, doc["ReqDocName"] or "Request")
                    parent.setText(6, doc["ReqDocType"] or "Request")
                    parent.setText(7, datetime.strptime(doc["CReqGeneratedTimeAt"], "%Y-%m-%dT%H:%M:%S").strftime("%d-%m-%Y %I:%M %p"))
                    parent.setText(8, doc["strCustomerName"])
                    parent.setText(9, doc["UserName"])
                    parent.setText(10, "Yes" if doc["has_xml"] else "No")
                    parent.setData(0, Qt.UserRole, {"type": "Request", "id": str(doc["ID"])})
            else:
                tree.clear()
            
            self.data_load_status[voucher_type] = True

        if self.tab_widget.tabText(self.tab_widget.currentIndex()) == voucher_type:
            self.page_label.setText(f"of {self.total_pages[voucher_type]}")
            self.page_input.setValue(self.current_page[voucher_type])
            self.status_bar.showMessage(f"Loaded {len(self.all_data[voucher_type])} records for {voucher_type}")
            self.update_pagination_buttons()

        self.pending_loads -= 1
        if self.pending_loads == 0:
            all_empty = all(not self.all_data[vt] for vt in VOUCHER_TYPES)
            if all_empty:
                start_date = self.start_date.date().toString("yyyy-MM-dd")
                end_date = self.end_date.date().toString("yyyy-MM-dd")
                QMessageBox.information(
                    self,
                    "Data Not Found",
                    f"No data found for any voucher type in the selected date range ({start_date} to {end_date})."
                )
                self.status_bar.showMessage("No data found for any voucher type")

    def handle_item_changed(self, item, column, voucher_type):
        if column != 0:
            return
        item.setCheckState(0, item.checkState(0))

    def change_page_size(self, size: str):
        self.page_size = int(size)
        for voucher_type in VOUCHER_TYPES:
            self.current_page[voucher_type] = 1
            self.progress_bars[voucher_type].setVisible(True)
            self.load_data(voucher_type)

    def prev_page(self):
        current_voucher_type = self.tab_widget.tabText(self.tab_widget.currentIndex())
        if self.current_page[current_voucher_type] > 1:
            self.current_page[current_voucher_type] -= 1
            self.progress_bars[current_voucher_type].setVisible(True)
            self.load_data(current_voucher_type)
        self.update_pagination_buttons()

    def next_page(self):
        current_voucher_type = self.tab_widget.tabText(self.tab_widget.currentIndex())
        if self.current_page[current_voucher_type] < self.total_pages[current_voucher_type]:
            self.current_page[current_voucher_type] += 1
            self.progress_bars[current_voucher_type].setVisible(True)
            self.load_data(current_voucher_type)
        self.update_pagination_buttons()

    def goto_page(self):
        try:
            current_voucher_type = self.tab_widget.tabText(self.tab_widget.currentIndex())
            page = self.page_input.value()
            if 1 <= page <= self.total_pages[current_voucher_type]:
                self.current_page[current_voucher_type] = page
                self.progress_bars[current_voucher_type].setVisible(True)
                self.load_data(current_voucher_type)
            else:
                QMessageBox.warning(self, "Invalid Page", f"Page must be between 1 and {self.total_pages[current_voucher_type]}")
                self.page_input.setValue(self.current_page[current_voucher_type])
            self.update_pagination_buttons()
        except ValueError:
            QMessageBox.warning(self, "Invalid Page", "Please enter a valid number")
            self.page_input.setValue(self.current_page[current_voucher_type])
            self.update_pagination_buttons()

    def download_selected(self):
        current_voucher_type = self.tab_widget.tabText(self.tab_widget.currentIndex())
        tree = self.trees[current_voucher_type]
        selected_requests = []
        for i in range(tree.topLevelItemCount()):
            parent = tree.topLevelItem(i)
            if parent.checkState(0) == Qt.Checked:
                selected_requests.append(parent.data(0, Qt.UserRole))

        if not selected_requests:
            QMessageBox.warning(self, "No Selection", "Please select at least one document")
            self.status_bar.showMessage("No selection")
            return

        file_count = len(selected_requests)
        reply = QMessageBox.question(
            self,
            "Confirm Import",
            f"Are you sure you want to import {file_count} file{'s' if file_count > 1 else ''} to Tally?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        if reply != QMessageBox.Yes:
            self.status_bar.showMessage("Import cancelled")
            return

        try:
            history_dir = os.path.join(CGeneralHelper.MSGetResourceDirectory(),"Manual_Import")
            if not os.path.exists(history_dir):
                os.makedirs(history_dir)
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_dir = os.path.join(history_dir, current_time)
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            headers = {
                "Authorization": f"Bearer {self.user_id}",
                "Content-Type": "application/json"
            }

            lsXMLFiles = []
            if len(selected_requests) == 1:
                record_id = selected_requests[0]["id"]
                has_xml = next((item.text(10) == "Yes" for item in tree.findItems(record_id, Qt.MatchExactly, 1)), False)
                if not has_xml:
                    QMessageBox.warning(self, "No XML", "Selected document has no XML content")
                    self.status_bar.showMessage("No XML")
                    return
               
                response = requests.post(
                    self.MultipleXMLDownloadURL,
                    # params={"user_id": self.user_id},
                    json=[record_id],
                    headers=headers
                )
                response.raise_for_status() 
                temp_zip_path = os.path.join(save_dir, f"documents_{self.user_id}.zip")
                with open(temp_zip_path, "wb") as f:
                    f.write(response.content)
                with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
                    zip_ref.extractall(save_dir)
                    for file_name in zip_ref.namelist():
                        if file_name.endswith('.xml'):
                            lsXMLFiles.append(os.path.join(save_dir, file_name))
                os.remove(temp_zip_path)
            else:
                request_ids = [int(req["id"]) for req in selected_requests]
               
                response = requests.post(
                    self.MultipleXMLDownloadURL,

                    json=request_ids,
                    headers=headers
                )
                response.raise_for_status()
                temp_zip_path = os.path.join(save_dir, f"documents_{self.user_id}.zip")
                with open(temp_zip_path, "wb") as f:
                    f.write(response.content)
                with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
                    zip_ref.extractall(save_dir)
                    for file_name in zip_ref.namelist():
                        if file_name.endswith('.xml'):
                            lsXMLFiles.append(os.path.join(save_dir, file_name))
                os.remove(temp_zip_path)

            try:
                dictLicenseData = CLicenseHelper.MSVerifyLicense()
                strToken = dictLicenseData["Token"]
                if not strToken:
                    QMessageBox.critical(self, "Error", "Failed to generate license token")
                    self.status_bar.showMessage("Token generation failed")
                    return
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to generate license token: {str(e)}")
                self.status_bar.showMessage("Token generation failed")
                return

            try:
                MSProcessXMLFiles(lsXMLFiles, strToken, current_time)
                self.status_bar.showMessage("XML(s) sent to server successfully")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to send XML(s) to server: {str(e)}")
                self.status_bar.showMessage("Server communication failed")

            QMessageBox.information(self, "Success", f"{file_count} file{'s' if file_count > 1 else ''} saved to {save_dir}")
            self.status_bar.showMessage("Import completed")
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "Error", f"Failed to download: {str(e)}")
            self.status_bar.showMessage("Import failed")
        except zipfile.BadZipFile:
            QMessageBox.critical(self, "Error", "Invalid ZIP file")
            self.status_bar.showMessage("Extraction failed")
        except PermissionError:
            QMessageBox.critical(self, "Error", "Permission denied to access the directory")
            self.status_bar.showMessage("Permission denied")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to process import: {str(e)}")
            self.status_bar.showMessage("Import failed")


def ShowActionableActivityUI():
    """Launch the Backup Report GUI"""
    try:

        # Check if QApplication already exists
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # Create and show the backup report window
        window = DocumentHistoryApp()
        window.show()

        # Only exec if this is the main application
        if app is not None and hasattr(app, 'exec_'):
            return app.exec_()

    except Exception as e:
        from CustomLogger import CLogger
        CLogger.MCWriteLog("error", f"Failed to show backup report GUI: {e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DocumentHistoryApp()
    window.show()
    sys.exit(app.exec_())