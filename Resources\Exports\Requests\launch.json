{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    // "H:\\AI Data\\19_ParagTraders_Project_2\\Research\\QuotationExcel\\TestQuotation\\AllItemNotAvailable.zip"
    // "H:\\AI Data\\19_ParagTraders_Project_2\\Research\\QuotationExcel\\TestQuotation\\Mitul Quotation Test.zip"
    // "H:\\AI Data\\19_ParagTraders_Project_2\\Research\\QuotationExcel\\TestQuotation\\QuotationExcelFileNotGiven.zip"
    // "H:\\AI Data\\19_ParagTraders_Project_2\\Research\\QuotationExcel\\TestQuotation\\Hansgrohe eServ. Inv. 6223151405 _ 15.01.2025.pdf"
    "version": "0.2.0",
    "configurations": [
    
        {
            "name": "Python Debugger: Python Export Filtered tag Stock Item",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["--export-filtered-stock-item"]
        },
        {
            "name": "Python Debugger: Python Process Delivery File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": [ "H:\\AI Data\\19_ParagTraders_Project_2\\Research\\QuotationExcel\\TestQuotation\\TestQuotationReport.zip","--delivery-note","--enable-export-stockitem"]
        },
        {
            "name": "Python Debugger: Python Process Purchase File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": [ "H:\\AI Data\\19_ParagTraders_Project_2\\Research\\QuotationExcel\\TestQuotation\\2411008342.pdf","--Journal"]
        },
        {
            "name": "Python Debugger: Python Process Purchase without Inventory",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\Customers\\Show Demo - AKS\\Purchase Without Inventory\\298_24-25.pdf","--purchase-without-inventory"]
        },
        {
            "name": "Python Debugger: Python Process Purchase with Inventory",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\Customers\\Show Demo - AKS\\Purchase With Inventory\\Simpolo_deskew_2431003311.pdf","--purchase-with-inventory", "--enable-export-stockitem"]
        },
        {
            "name": "Python Debugger: Python Process Activity Logs",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["--get-activity-report"]
        },
        {
            "name": "Python Debugger: Python Process Purchase Without Inventory",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\Customers\\Prem Textiles\\Manual Testing\\9646.pdf","--purchase-without-inventory"]
        },
        {
            "name": "Python Debugger: Python Process Purchase Without Inventory The Ahmedabad ",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\AI Data\\26_Gwalia\\17_TheAhmedabadCoopDeptStoriesLtd\\2149.pdf","--purchase-without-inventory"]
        },
        {
            "name": "Python Debugger: Python Process Purchase Without Inventory Shri Ram Enterprise ",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\AI Data\\26_Gwalia\\13_ShriRamEnterprise\\1946.pdf","--purchase-without-inventory"]
        },
        {
            "name": "Python Debugger: Python Process Purchase Without Inventory Sygnia",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\AI Data\\26_Gwalia\\16_Sygnia\\12540.pdf","--purchase-without-inventory"]
        },
        {
            "name": "Python Debugger: Python Process Purchase Without Inventory bhavyaSales",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\AI Data\\26_Gwalia\\5_BhavyaSalesCompany\\304_24-25.pdf","--purchase-without-inventory"]
        },
        {
            "name": "Python Debugger: Python Process Purchase Without Inventory sheeba diary",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\AI Data\\26_Gwalia\\1_Sheeba Dairy\\3894.pdf","--purchase-without-inventory"]
        },
        {
            "name": "Python Debugger: Python Process Purchase Without Inventory Multiple Vendor Single File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\Customers\\26_Gwalia\\Manual Testing\\AV-Multiple-Vendor-Single-Doc.pdf","--purchase-without-inventory"]
        },
        {
            "name": "Python Debugger: Python Process Purchase Without Inventory Multiple Vendor Single File ZIP File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\Customers\\26_Gwalia\\Manual Testing\\Multiple Vendor 2 PDF.zip","--purchase-without-inventory"]
        },
        {
            "name": "Python Debugger: Python Process Purchase Without Inventory Multiple 3 Vendor Single File ZIP File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\Customers\\26_Gwalia\\Manual Testing\\Multiple Vendor 2 Doc.pdf","--purchase-without-inventory"]
        },
        {
            "name": "Python Debugger: Python Process Purchase Without Inventory Shree Foods",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\Customers\\26_Gwalia\\Manual Testing\\01797.pdf","--purchase-without-inventory"]
        },
        {
            "name": "ICD: Concor Vendor",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\Customers\\ICD Dhannad\\Manual Testing\\Concor\\concor 1 doc.zip","--Journal"]
        },
        {
            "name": "Python Debugger: Python Process Bank Statement",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "args": ["H:\\AI Data\\Gwalia\\demo1.xlsx","--Journal"]
        }
    ]
}