;OM ;<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON>; <PERSON>

;Author - <PERSON><PERSON><PERSON> | Owner - AccuVelocity | Version - 3 Beta | Date - 29th January 2025

;DefVoucherType:"Delivery Note" ;"Purchase With Inventory"

[System: Variable]
BatchFilePAth: "H:\AI Data\ClearData.bat"
BackUpDestinationPathLoc : "C:\Users\<USER>\TallyPrime\AccuVelocity\Backup Before Import"
TallyComDataPath:"H:\TallyTempCompany\Companies\data"
AVExefilePathLoc:"C:\Users\<USER>\TallyPrime\AccuVelocity\AVDocProcessor.exe"
AVHelpFilePath:"C:\Users\<USER>\TallyPrime\AccuVelocity\Resources\AVHelpDoc\accuveloHelp.html"
EnableStockItemInvExport: Yes


[Variable: BatchFilePAth]
Type : String
;Persistent : Yes
[Variable: BackUpDestinationPathLoc]
Type : String
;Persistent : Yes
[Variable: AVE<PERSON>lePathLoc]
Type : String
[Variable: AVHelpFilePath]
Type : String
;Persistent : Yes
[Variable: TallyComDataPath]
Type : String
;Persistent : Yes
[Variable: EnableStockItemInvExport]
Type : String
;Persistent : Yes
;[Variable: DefVoucherType]
;Type : String
;Persistent : Yes


;DefVoucherType:"Delivery Note"
;Clear Pipeline Button used to clear data from Tally API server
[Button:ClearPipeLine]
	Key:Ctrl+F
	Title:Finish Import
	Action:Browse Url Ex:##BatchFilePAth ;Replace With AVPipelineClear bat file path
	;Action:Browse Url Ex:$ClearPiplineBatPath;Replace With AVPipelineClear bat file path -- TODO---
[Button:ShowHelpDoc]
	Key:Ctrl+H
	Title:AccuVelocity Help
	Action:Browse Url Ex:##AVHelpFilePath
;Takes Back	
[Button:TakeBackUp]
	Key:Ctrl+N
	Title:BackUp
	Action:Call:Backup Company AV;Replace With AVPipelineClear bat file path
	Inactive: NOT $BTakeBackup
	
	
;Adding Clear Pipeline Button to Tally
[#Menu:Gateway of Tally]
	Buttons:ClearPipeLine
	
;Adding SubMenu of AccuVelocity to Tally
[#Menu : Gateway of Tally]
	Add:Key Item:After:@@locIndentUtilities: AccuVelocityAI:V:Menu:AccuVelocity AI
	Add:Indent:After:@@locIndentUtilities:AccuVelocityAI
	
;Adding Items to SubMenu of AccuVelocity to Tally
[Menu: AccuVelocityAI] 
	Add:Indent:After:Indent:Process
	Add:Item:AccuVelocity Voucher Processing:Create:ExeLoc
	Add:Button:ClearPipeLine ;Adding Clear Pipeline Button used to clear data from Tally API server to SubMenu
	Add:Button:ShowHelpDoc
	;Add:Indent:After:Indent:Info
	Add:Item:AccuVelocity Help:Browse URL:##AVHelpFilePath
	
;Adding new report	
[Report : ExeLoc]
	Title: Process Voucher With AccuVelocity
	Form : ExeLocForm
	Add	: Variable	: vCurrentPath
	Set	: vCurrentPath	: If $$IsEmpty:$Fpath Then "C:\Program Files\TallyPrime" Else $$GetParentDirectory:$Fpath ;setting Path where the folder will open by defult
	

;Adding new Form	
[Form : ExeLocForm]
	Part : ExeLocPart,ExeLocPart3,ExeLocPart2
	Width: 80% page
	Height:40% page
	Add:Button:CallExe
	Add:Button:ClearPipeLine ;Adding Clear Pipeline Button used to clear data from Tally API server to SubMenu
	Add:Button:TakeBackUp
	Add:Button:ShowHelpDoc
	On:Form Accept:Yes:Call:ProcessALL
	Persist:True
;	No Confirm:True
;	Confirm Text:"TRw"

;Adding new Part to Display Only 3 Lines - Exe Path, Select File and Path of Selected file	
[Part : ExeLocPart]
	Line : TopLine1,ExeLocLine3,ExeLocLine15,ExeLocLine,ExeLocLine2,ExeLocLine12 ;ExeLocLine13 ExeLocLine14 - BackUp 
	;Scroll:Horizontal
	;Repeat:ExeLocLine3:CDCollection
	Border:Thick Bottom
	Height:16% page
	
;[Part : ExeLocPart4]
;	Line:Templine
;	Repeat:Templine:CDCollection
	
[Line:TopLine1]
	Field:Form SubTitle
	Local:Field:Form SubTitle:Set as: "AccuVelocity AI"
	;Local:Field:Form SubTitle:Background:lightblue 
	
;Adding New Lines - Select File
[Line:ExeLocLine]
	Field:Medium prompt ,ExeLocField
	Local:Field:Medium prompt : Set as:"Select File"
	
[Line:ExeLocLine12]
	Field:Long prompt,BackUpField
	Local:Field:Long prompt : Set as:"BackUp Before Import:"
	
[Line:ExeLocLine13]
	Field:Medium prompt,BackUpDestinationfolderSelection,
	Local:Field:Medium prompt: Set as: "BackUp Destination"
	Local:Field:Medium prompt:Inactive:NOT $BTakeBackup
	
[Line:ExeLocLine14]
	Field:Long prompt,ValueOfBackUPPath
	Local:Field:Long prompt: Set as: "Selected BackUp Destination"
	Local:Field:Long prompt:Inactive:NOT $BTakeBackup

[Line:ExeLocLine15]
	Field:Long prompt,TypeOfVChAV
	Local:Field:Long prompt:Set as:"Selecte Voucher type"
	
;Adding New Lines - Path of Selected file	
[Line:ExeLocLine2]
	Field:Long prompt,ExeLocFieldup
	Local:Field:Long prompt:Set as:"Selected File Path To Process"
	
;Adding New Lines - Exe Path
[Line:ExeLocLine3]
	Field:Medium prompt ,Exe StaticPath
	Local:Field:Medium prompt : Set as:"AccuVelocity Exe"

;Adding New Field to Select File
[Field : ExeLocField]
	Use:file selection template
	Storage:AVExeFilePath
	Align:Left
	Width:50% Page
	
[Field : BackUpField]
	Use:Logical Field
	Show Table: Always
	Storage: BTakeBackup
	Align:Left
	Width:50% Page
	
[Field : BackUpDestinationfolderSelection]
	Set as:##BackUpDestinationPathLoc+ "\" + @@DateForm + "\" + @@TimeForm
	;Set as:"H:\Temp" + + "\" + @@DateForm + "\" + @@TimeForm
	Use:Name Field
;	Use: Folder selection template
	Storage:AVBackUpDir
	Align:Left
	Width:50% Page
	Inactive: NOT $BTakeBackup
	
[Field : ValueOfBackUPPath]
	Use:Name Field
	Set as: If $$IsEmpty:#BackUpDestinationfolderSelection Then "" Else #BackUpDestinationfolderSelection  + "\" + @@DateForm + "\" + @@TimeForm
	Set Always:Yes
	Align:Left
	Width:50% Page
	Inactive: NOT $BTakeBackup
	Read Only:Yes
	
;Adding New Field display Path to EXE
[Field : Exe StaticPath]
	Use:Name field
	Set as: ##AVExefilePathLoc
	;Set as: "D:\Customer\REAL\TallyAccuVelocity\AVDocProcessor.exe" ;Replace With AVVoucherProcess.exe file path
	;Set as:$$CollectionField:$AVExeLocation:1:CDCollection ;Replace With AVVoucherProcess.exe file path
	Set Always:Yes
	Skip:On  								;Does not allow us to go on that Field
	Align:Left
	Width:50% Page
	Read Only:Yes							;Does not allow us to edit that Field

;Adding New Field display Path of Selected file
[Field : ExeLocFieldup]
	Use:Name field
	Set as: If $$IsEmpty:#ExeLocField Then ##FileLoc Else $$GetFullPath:#ExeLocField ;Showing Value of Field EXELocField
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Storage:Fpath
	Width:50% Page
	
[Field : TypeOfVChAV]
	Use:Name field
	Set as: If $$IsEmpty:$$Value then "Purchase With Inventory" else $$Value ;Showing Value of Field EXELocField
	;Set as: If $$IsEmpty:$$Value then $$CollectionField:$VOUCHERTYPEDEF:1:CDCollection else $$Value ;Showing Value of Field EXELocField
	Set Always:Yes
	Align:Left
	Show Table:Always
	Storage:InvoiceType
	Table:TyAV
	Width:50% Page
	
[Table : TyAV]
	Title : $$LocaleString:"Type of Vouchers"
	List Name :Purchase With Inventory,Delivery Note,Journal Entry
	Format : $Name ,20
	

;Adding Button to call Exe
[Button:CallExe]
	Key:Ctrl+P
	Title:Process Voucher
	ADD:Action:Call:DecideVoucherType
	;Action:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' ;Passing AVVoucherProcess.exe "FullPathOfFileToBeProcessed"
	
;creating a UDF to StoreValue of Seleceted File Path
[System:udf]
	AVExeFilePath:String:22445
	Fpath:String:22446
	BTakeBackup:Logical:22447
	AVBackUpDir:String:22448
	InvoiceType:String:22449
	
;A defatult value to Exe to store Path
[Variable: FileLoc]
	Default		:##AVExefilePath
	Type		: String
	Persistent	: Yes

;A Variable to store defatult 
[System: Variable]
	Variable	: FileLoc
	
[Function: ProcessALL]
	01:If: #BackUpField = "Yes"
		02:Call:Backup Company AV
		03:Call:DecideVoucherType
	04:Else:
		05:Call:DecideVoucherType
	06:End If
	
[Function: DecideVoucherType]
	Variable: SelectedVoucherTypeAV:String
	Variable: cmdstr :String
	01:If: #TypeOfVChAV = "Purchase With Inventory"
		02:Set:SelectedVoucherTypeAV : " --purchase-with-inventory"
		03:Log:##SelectedVoucherTypeAV
			04:If :##EnableStockItemInvExport
				05:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
				06:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
			07:Else
				08:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
				09:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV
			10:End If
		11:Log:##cmdstr
		12:Return
	13:Else
		14:If: #TypeOfVChAV = "Delivery Note"
			15:Set:SelectedVoucherTypeAV :" --delivery-note"
			16:Log:##SelectedVoucherTypeAV
			16a:Log:##EnableStockItemInvExport
			17:If :##EnableStockItemInvExport
				18:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
				19:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
			20:Else
				21:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
				22:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV
			23:End If
			24:Log:##cmdstr
			25:Return
		26:Else:
			27:If:#TypeOfVChAV = "Journal Entry"
				28:Set:SelectedVoucherTypeAV :" --Journal"
				29:Log:##SelectedVoucherTypeAV
				30:If :##EnableStockItemInvExport
					31:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
					32:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV + " --enable-export-stockitem"
				33:Else
					34:Set: cmdstr:#ExeStaticPath + ":" + '"' + $Fpath + '"' + ##SelectedVoucherTypeAV
					35:Browse Url:#ExeStaticPath:'"' + $Fpath + '"' + ##SelectedVoucherTypeAV
				36:End If
				37:Log:##cmdstr
				38:Return
			39:End If
		40:End If
		;12:MSGBOX:"VAl":##SelectedVoucherTypeAV
	41:End If
	
;Adding new Part to Display Step
[Part : ExeLocPart3]
	Line:ExeLocLine10
;	Background:LightBlue
;	Height:20% page
	
[Part : ExeLocPart2]
	Line:ExeLocLine4,ExeLocLine5,ExeLocLine6,ExeLocLine7,ExeLocLine17,ExeLocLine8,ExeLocLine9,ExeLocLine16,ExeLocLine11
	Background:LightBlue	
[Line:ExeLocLine10]
	Field:UserMsgFeild7
[Line:ExeLocLine11]
	Field:UserMsgFeild8,UserMsgFeild9
[Line:ExeLocLine4]
	Field:short prompt ,UserMsgFeild
	Local:Field:short prompt  : Set as:"Step 1 :"
[Line:ExeLocLine5]
	Field:short prompt ,UserMsgFeild2
	Local:Field:short prompt  : Set as:"Step 2 :"
[Line:ExeLocLine6]
	Field:short prompt ,UserMsgFeild3
	Local:Field:short prompt  : Set as:"Step 3 :"
[Line:ExeLocLine7]
	Field:short prompt  ,UserMsgFeild4
	Local:Field:short prompt : Set as:"Step 4 :"
[Line:ExeLocLine17]
	Field:short prompt  ,UserMsgFeild52
	Local:Field:short prompt  : Set as:"Step 5 :"
[Line:ExeLocLine8]
	Field:short prompt  ,UserMsgFeild5
	Local:Field:short prompt  : Set as:"Step 6 :"
[Line:ExeLocLine9]
	Field:short prompt ,UserMsgFeild6
	Local:Field:short prompt : Set as:"Step 7 :"
[Line:ExeLocLine16]
	Field:short prompt ,UserMsgFeild10
	Local:Field:short prompt : Set as:"Step 8 :"
	
[Field : UserMsgFeild]
	Use:Narration Field
	Set as:"Ensure that your executable (EXE) file is located in the specified path provided in the AccuVelocity EXE field."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild2]
	Use:Narration Field
	Set as:"Select the type of voucher you would like to process from the available options."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild3]
	Use:Narration Field
	Set as:"Select the file you wish to process using AccuVelocity AI."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild4]
	Use:Narration Field
	Set as:"Verify that the full path of the selected file appears in the 'Selected File Path To Process' field."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild52]
	Use:Narration Field
	Set as:"To back up the selected company before import, choose Yes in BackUpBeforeImport, or No if not."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild5]
	Use:Narration Field
	Set as:"Press Ctrl + V or Ctrl + A or click the Process Voucher button to send the selected file to AccuVelocity for processing."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:70% Page
	Skip:On
[Field : UserMsgFeild6]
	Use:Narration Field
	Set as:"Repeat the same steps for additional files or quit once the processing is complete."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild10]
	Use:Narration Field
	Set as:"You may exit the application once processing has started. A notification email will be sent once the data has been successfully received."
	Set Always:Yes
	Align:Left
	Read Only:Yes
	Width:70% Page
	Skip:On
[Field : UserMsgFeild7]
	Use:AsRightSubTitle
	Set as:"Guidelines for Using the AccuVelocity Voucher Process:"
	Set Always:Yes
	Align:Left
	Style:normal Bold
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild8]
	Use:AsRightSubTitle
	Set as:"Version - 3 Beta"
	Set Always:Yes
	Align:Left
	Style:normal Bold
	Read Only:Yes
	Width:50% Page
	Skip:On
[Field : UserMsgFeild9]
	Use:AsRightSubTitle
	Set as:"Date - 30th January 2025"
	Set Always:Yes
	Align:Right
	Style:normal Bold
	Read Only:Yes
	Width:50% Page
	Skip:On
[System: Formula]
	CoNumber : $$String:($CmpNumStr:Company:##SVCurrentCompany):5
	DateForm : $$String:$$MachineDate
	TimeForm : @@HrsForm + @@MtsForm
	HrsForm : If $$StringPart:$$MachineTime:0:2 CONTAINS ":" +
	Then $$StringPart:$$MachineTime:0:1 +
	Else $$StringPart:$$MachineTime:0:2
	MtsForm : if $$StringPart:$$MachineTime:0:2 CONTAINS ":" +
	Then $$StringPart:$$MachineTime:2:2 +
	Else $$StringPart:$$MachineTime:3:2
[Function: Backup Company AV]
	Variable : BackupDetVarAV: String
	Variable: TallyDataPath : String: ##TallyComDataPath
	Variable: BackUpDest: String: ##BackUpDestinationPathLoc + "\" + @@DateForm + "\" + @@TimeForm
	;10 : SET : BackupDetVarAV : #ValueOfBackUPPath + ", " + ##SVCurrentPath + ", " + ##SVCurrentCompany + + ", " + @@CoNumber
	10 : SET : BackupDetVarAV : ##BackUpDest + ", " + ##TallyComDataPath + ", " + ##SVCurrentCompany + + ", " + @@CoNumber
	;20 : MSGBOX :"Return Value" : ##BackupDetVarAV
	30 : LOG :##BackUpDest
	31 : Log:##TallyComDataPath
	32 : Log: ##SVCurrentCompany
	33 : Log: @@CoNumber
	34 : Log: ##BackupDetVarAV
	40 : BACKUP COMPANY : ", " : ##BackupDetVarAV
	
