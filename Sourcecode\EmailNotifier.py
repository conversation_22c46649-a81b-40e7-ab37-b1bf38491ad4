import pandas as pd
import smtplib
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import traceback
import os
from pathlib import Path
from datetime import datetime

class EmailSender:
    @staticmethod
    def send_email(from_address, password, to_addresses, cc_addresses, subject, html_content, smtp_server, smtp_port, lsAttachmentPath=[]):
        """
        Sends an email with the given parameters.

        :param from_address: Sender's email address
        :param password: Sender's email password
        :param to_addresses: List of recipient email addresses
        :param cc_addresses: List of CC email addresses
        :param subject: Subject of the email
        :param html_content: HTML content of the email
        :param smtp_server: SMTP server address
        :param smtp_port: SMTP server port
        """
        # Create the email message
        message = MIMEMultipart("alternative")
        message["From"] = from_address
        message["To"] = ", ".join(to_addresses)
        message["Cc"] = ", ".join(cc_addresses)
        message["Subject"] = subject

        # Attach the HTML content
        message.attach(MIMEText(html_content, "html"))

        if lsAttachmentPath:
            for attachment_path in lsAttachmentPath:
                try:
                    if os.path.isfile(attachment_path):
                        with open(attachment_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                            encoders.encode_base64(part)
                            part.add_header('Content-Disposition', f'attachment; filename={os.path.basename(attachment_path)}')
                            message.attach(part)
                    else:
                        print(f"Attachment file does not exist: {attachment_path}")
                except Exception as e:
                    print(f"Failed to add attachment: {attachment_path} in the email: {e}")
            
        # Combine all recipients for sending
        all_recipients = to_addresses + cc_addresses

        try:
            # Setup the SMTP server
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(from_address, password)
                server.sendmail(from_address, all_recipients, message.as_string())
            print("Email sent successfully!")
        except Exception as e:
            print(f"Error: {e}")

    @staticmethod
    def generate_api_error_html(error_type: str, error_detail: str, template_path: str = "Resources\APIHealthMailforamt.html", system_name = "", client_name = "") -> str:
        try:
            current_time = datetime.now().strftime("%d/%m/%Y : %H:%M")
            with open(template_path, "r", encoding="utf-8") as file:
                template = file.read()
                html_content = template.replace("{{error_type}}", error_type).replace("{{error_detail}}", error_detail).replace("{{username}}",system_name).replace("{{date}}",current_time).replace("{{client}}",client_name)
                return html_content
        except FileNotFoundError:
            return f"<html><body><p>Error: Could not find HTML template at {template_path}</p></body></html>"
        except Exception as e:
            return f"<html><body><p>Error loading template: {str(e)}</p></body></html>"