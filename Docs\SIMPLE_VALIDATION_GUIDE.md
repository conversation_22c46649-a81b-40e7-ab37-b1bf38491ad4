# AccuVelocity System Status Check - User Guide

## Overview

The AccuVelocity System Status Check is a simple, user-friendly tool designed specifically for accountants to:
- ✅ Check if everything is working properly
- 🔧 Get step-by-step help to fix common issues
- 📞 Easily contact support when needed

## Key Features

### 🎯 **Simplified for Accountants**
- **No Technical Jargon**: Everything is explained in simple terms
- **Clear Status Indicators**: Green ✅ = Good, Orange ⚠️ = Needs attention, Red ❌ = Problem
- **Step-by-Step Guidance**: Clear instructions on how to fix issues
- **Easy Support Access**: One-click access to help and support

### 🔧 **What It Checks**
1. **Configuration Files** - System configuration files
2. **Tally Connection** - Connection to TallyPrime software
3. **AccuVelocity Server** - Connection to AccuVelocity server
4. **User Settings** - Your personal configuration settings

### 🚀 **How to Use**

#### Launch the System Check
```python
# From your main application
from SimpleValidationLauncher import launch_system_check

# Launch the system status check
dashboard = launch_system_check()
```

#### Or run directly
```bash
python SimpleValidationLauncher.py
```

## User Interface Guide

### Main Window
- **System Status Overview** (Left Panel): Shows the status of all components
- **Details Panel** (Right Panel): Shows warnings, information, and actions

### Buttons
- **🔄 Retry Again**: Run the checks again after fixing issues
- **⚙️ Settings**: Configure Tally port and file locations
- **❓ Need Help?**: Get help and support information

### Status Colors
- 🟢 **Green**: Everything is working fine
- 🟡 **Orange**: There's a warning - should be checked
- 🔴 **Red**: There's a problem that needs to be fixed
- ⚪ **Gray**: Status unknown or checking in progress

## Settings Configuration

### Tally Settings Dialog
When you click **⚙️ Settings**, you can configure:

1. **Tally Port**: Usually 9000 (the port TallyPrime uses)
2. **Company Data Location**: Where your Tally company data is stored
3. **Tally Application INI File**: Location of the Tally.ini file

### How to Configure Tally Port
1. Open TallyPrime
2. Go to **Help** → **Settings** → **Connectivity** → **Client/Server Configuration**
3. Make sure:
   - **TallyPrime acts as**: Both
   - **Enable ODBC**: Yes
   - **Port**: 9000

## Common Issues & Solutions

### 🔴 Tally Not Connected
**Problem**: Can't connect to TallyPrime

**Solutions**:
1. Make sure TallyPrime is running
2. Check if the port is correct (usually 9000)
3. In TallyPrime settings, ensure ODBC is enabled
4. Try restarting TallyPrime

### 🔴 AccuVelocity Server Not Connected
**Problem**: Can't connect to AccuVelocity server

**Solutions**:
1. Check your internet connection
2. Wait a few minutes and try again
3. Contact support if problem continues

### 🔴 Configuration Files Missing
**Problem**: System can't find required files

**Solutions**:
1. Use the **⚙️ Settings** button to set correct file locations
2. Make sure network drives are connected
3. Check file permissions

## Getting Help & Support

### Built-in Help
- Click **❓ Need Help?** for comprehensive help
- Click **Get Help** in the warnings section for specific guidance

### Contact Support
- **📧 Email**: <EMAIL>
- **📱 Phone**: +91 98989 42935

### Support Hours
- **Monday to Friday**: 10:30 AM to 8:00 PM IST

## Integration with Your Application

### Simple Integration
Add a system check button to your main application:

```python
from SimpleValidationLauncher import launch_system_check

class YourMainApp:
    def __init__(self):
        # Add system check button
        self.system_check_btn = QPushButton("🔍 Check System Status")
        self.system_check_btn.clicked.connect(self.check_system_status)

    def check_system_status(self):
        """Launch system status check"""
        launch_system_check()
```

### Pre-Processing Check
Check system health before important operations:

```python
from ValidationLauncher import ValidationLauncher

def before_processing_documents(self):
    """Check system health before processing"""
    health = ValidationLauncher.check_system_health()

    if health['critical_count'] > 0:
        reply = QMessageBox.question(self, "System Issues Found",
            f"Found {health['critical_count']} issue(s) that may affect processing.\n\n"
            "Would you like to check and fix these issues first?")

        if reply == QMessageBox.Yes:
            launch_system_check()
            return False  # Don't proceed with processing

    return True  # OK to proceed
```

## Support Information
- **📧 Email**: <EMAIL>
- **📱 Phone**: +91 98989 42935
- **🕒 Hours**: Mon-Fri 9AM-6PM, Sat 10AM-4PM IST