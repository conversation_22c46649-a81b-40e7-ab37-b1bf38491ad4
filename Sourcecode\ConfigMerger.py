# This file is used to merge the user config and developer config json files at the time of installation using installer.

import json
import sys
import os
from EncryptResourceConfig import CReadDeveloperConfig, DEFAULT_KEY

def deep_merge(default, user):
    for k, v in default.items():
        if k not in user:
            user[k] = v
        elif isinstance(v, dict) and isinstance(user[k], dict):
            deep_merge(v, user[k])

def is_developer_config(file_name):
    return "DeveloperConfig" in os.path.basename(file_name)

def load_json(file_path, decrypt=False, key=DEFAULT_KEY):
    if decrypt:
        decrypted_data = CReadDeveloperConfig.MSDecryptDeveloperConfig(file_path, key)
        if decrypted_data is None:
            raise ValueError(f"Decryption failed for: {file_path}")
        return json.loads(decrypted_data.decode())
    else:
        with open(file_path, "r") as f:
            return json.load(f)

def save_json(file_path, data, encrypt=False, key=DEFAULT_KEY):
    temp_file = file_path + ".tmp"
    with open(temp_file, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=4)
    if encrypt:
        CReadDeveloperConfig.MSEncryptDeveloperConfig(temp_file, key)
        os.replace(temp_file, file_path)
    else:
        os.replace(temp_file, file_path)

def main():
    if len(sys.argv) != 3:
        sys.exit("Usage: merge_config.py default.json user.json")

    dpath, upath = sys.argv[1:]

    decrypt_default = is_developer_config(dpath)
    decrypt_user = is_developer_config(upath)

    # Load and decrypt if needed
    default_config = load_json(dpath, decrypt_default)
    user_config = load_json(upath, decrypt_user)

    # Merge default into user
    deep_merge(default_config, user_config)

    # Save and encrypt if needed
    save_json(upath, user_config, encrypt=decrypt_user)

    print(f"Config merged successfully into: {upath}")

if __name__ == "__main__":
    main()
