import pandas as pd
import smtplib
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import os


class EmailSender:
    @staticmethod
    def send_email(from_address, password, to_addresses, cc_addresses, subject, html_content, smtp_server, smtp_port, lsAttachmentPath=[]):
        """
        Sends an email with the given parameters.

        :param from_address: Sender's email address
        :param password: Sender's email password
        :param to_addresses: List of recipient email addresses
        :param cc_addresses: List of CC email addresses
        :param subject: Subject of the email
        :param html_content: HTML content of the email
        :param smtp_server: SMTP server address
        :param smtp_port: SMTP server port
        """
        # Create the email message
        message = MIMEMultipart("alternative")
        message["From"] = from_address
        message["To"] = ", ".join(to_addresses)
        message["Cc"] = ", ".join(cc_addresses)
        message["Subject"] = subject

        # Attach the HTML content
        message.attach(MIMEText(html_content, "html"))

        if lsAttachmentPath:
            try:
                for attachment_path in lsAttachmentPath:
                    if os.path.isfile(attachment_path):
                        with open(attachment_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                            encoders.encode_base64(part)
                            part.add_header('Content-Disposition', f'attachment; filename={os.path.basename(attachment_path)}')
                            message.attach(part)
                    else:
                        print(f"Attachment file does not exist: {attachment_path}")
            except Exception as e:
                print(f"Failed to add attachments in the email: {e}")
            
        # Combine all recipients for sending
        all_recipients = to_addresses + cc_addresses

        try:
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()  # Upgrade connection to TLS
                server.login(from_address, password)
                server.sendmail(from_address, all_recipients, message.as_string())


            # Setup the SMTP server
            # with smtplib.SMTP(smtp_server, smtp_port) as server:
            #     server.starttls()
            #     server.login(from_address, password)
            #     server.sendmail(from_address, all_recipients, message.as_string())
            print("Email sent successfully!")
        except Exception as e:
            print(f"Error: {e}")

def SendTallyNotificationEmail(strReceiverName, strSubject, strMailFrom, lsMailTo, strServer, intPort, strPassword, strAVVersion, strAVTDLVersion, strMacAddress, htmlTemplatePath=r"Resources\Email_Templates\StockItemExportError.html", lsCC=[], lsAttachmentPath=[]):
    
    # Read HTML Template from the File
    try:
        with open(htmlTemplatePath, 'r') as html_file:
            html_template = html_file.read()
    except FileNotFoundError:
        print(f"Error: The HTML template file {htmlTemplatePath} was not found.")
        return
    except Exception as e:
        print(f"Error reading HTML template file: {e}")
        return
    
    # Populate the HTML Template with Dynamic Content
    html_content = html_template.format(
        date=pd.Timestamp.today().strftime('%d/%m/%Y'),
        receiverName = strReceiverName,
        av_version=strAVVersion,
        av_tdl_version=strAVTDLVersion,
        mac_address=strMacAddress
    )

    # Send the email
    EmailSender.send_email(
        from_address=strMailFrom,
        password=strPassword,
        to_addresses=lsMailTo,
        cc_addresses=lsCC,
        subject=strSubject,
        html_content=html_content,
        smtp_server=strServer,
        smtp_port=intPort, 
        lsAttachmentPath=lsAttachmentPath
    )

if __name__ == "__main__":
    
    SendTallyNotificationEmail(
                            strReceiverName="AV DEV",
                            strSubject="Accuvelocty Document Processing For Airen Group",
                            strMailFrom="<EMAIL>",
                            lsMailTo=['<EMAIL>'],
                            strPassword="WSuNegdfaGA5",
                            lsCC=["<EMAIL>"],
                            strServer="smtppro.zoho.in",
                            intPort=int("587"),
                            strAVVersion=11.1,
                            strAVTDLVersion=12.1,
                            strMacAddress="3ed"
                        )