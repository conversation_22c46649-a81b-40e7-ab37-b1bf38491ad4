SR. NO,Voucher Type,Vendor / Bank / Document Name,AccuVelocity Validation,AccuVelocity PriceList Verification,AccuVelocity NOTE
1,PV_WITH_INVENTORY,SIMPOLO,"1. Connect stock items and verify the availability of the item name in the inventory. If the item is not available, mention it in the AccuVelocity comments.
2. Verify the price list for each item on the invoice.
3. Ensure no new item or ledger creation is done in your Tally; instead, use the existing available stock item data.
4. Generate weekly and monthly automated price list merge reports for your processed invoices.",YES,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
2,PV_WITH_INVENTORY,NEXION,"1. Connect stock items and verify the availability of the item name in the inventory. If the item is not available, mention it in the AccuVelocity comments.
2. Verify the price list for each item on the invoice.
3. Ensure no new item or ledger creation is done in your Tally; instead, use the existing available stock item data.
4. Generate weekly and monthly automated price list merge reports for your processed invoices.",YES,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
3,PV_WITH_INVENTORY,ICON,"1. Connect stock items and verify the availability of the item name in the inventory. If the item is not available, mention it in the AccuVelocity comments.
2. Verify the price list for each item on the invoice.
3. Ensure no new item or ledger creation is done in your Tally; instead, use the existing available stock item data.
4. Generate weekly and monthly automated price list merge reports for your processed invoices.",YES,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
4,PV_WITH_INVENTORY,HANSGROHE,"1. Connect stock items and verify the availability of the item name in the inventory. If the item is not available, mention it in the AccuVelocity comments.
2. Verify the price list for each item on the invoice.
3. Ensure no new item or ledger creation is done in your Tally; instead, use the existing available stock item data.
4. Generate weekly and monthly automated price list merge reports for your processed invoices.",YES,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
5,PV_WITH_INVENTORY,KOHLER,"1. Connect stock items and verify the availability of the item name in the inventory. If the item is not available, mention it in the AccuVelocity comments.
2. Verify the price list for each item on the invoice.
3. Ensure no new item or ledger creation is done in your Tally; instead, use the existing available stock item data.
4. Generate weekly and monthly automated price list merge reports for your processed invoices.",YES,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
6,PV_WITH_INVENTORY,TOTO,"1. Connect stock items and verify the availability of the item name in the inventory. If the item is not available, mention it in the AccuVelocity comments.
2. Verify the price list for each item on the invoice.
3. Ensure no new item or ledger creation is done in your Tally; instead, use the existing available stock item data.
4. Generate weekly and monthly automated price list merge reports for your processed invoices.",YES,"1. It is preferable to use digital documents then .
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
7,PV_WITH_INVENTORY,GEBERIT,"1. Connect stock items and verify the availability of the item name in the inventory. If the item is not available, mention it in the AccuVelocity comments.
2. Verify the price list for each item on the invoice.
3. Ensure no new item or ledger creation is done in your Tally; instead, use the existing available stock item data.
4. Generate weekly and monthly automated price list merge reports for your processed invoices.",YES,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
8,PV_WITH_INVENTORY,POWERGRACE,"1. Connect stock items and verify the availability of the item name in the inventory. If the item is not available, mention it in the AccuVelocity comments.
2. Ensure no new item or ledger creation is done in your Tally; instead, use the existing available stock item data.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
9,DELIVERY_NOTE,SANITARY QUOTATION,"1. Connect stock items and verify the availability of the item name in the inventory. If the item is not available, mention it in the AccuVelocity comments.
2. Ensure no new item or ledger creation is done in your Tally; instead, use the existing available stock item data.",NO,1. AccuVelocity only supports the Sanitary Quotation in Excel format
10,JOURNAL_VOUCHER,j.r. techno-chem & consultansts,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
11,JOURNAL_VOUCHER,amrit tripmakers,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
12,JOURNAL_VOUCHER,shree radha krishna enterprises,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
13,JOURNAL_VOUCHER,shree shantinath traders,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
14,JOURNAL_VOUCHER,novel exiconn pvt. ltd.,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
15,JOURNAL_VOUCHER,shree ram enterprise,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
16,JOURNAL_VOUCHER,maa narmada logistics,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
17,JOURNAL_VOUCHER,shri mahankaal roadlines,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
18,JOURNAL_VOUCHER,Kamal Kishore,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
19,JOURNAL_VOUCHER,supreme auto centre,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
20,JOURNAL_VOUCHER,CD MULTIMEDIA GALLERY,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
21,JOURNAL_VOUCHER,SAINATH ENGINEERING SERVICES,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
22,JOURNAL_VOUCHER,MOHIT ENTERPRISES,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
23,JOURNAL_VOUCHER,MANGAL HARDWARE AND PAINTS,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
24,JOURNAL_VOUCHER,GULAB HARDWARE STORES,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
25,PV_WITHOUT_INVENTORY,rollin logistic,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
26,PV_WITHOUT_INVENTORY,kiron electricals,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
27,PV_WITHOUT_INVENTORY,bharat sanchar nigam limited,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
28,PV_WITHOUT_INVENTORY,bharti airtel limited,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
29,JOURNAL_VOUCHER,VHD DISTRIBUTOR LLP,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
30,JOURNAL_VOUCHER,csc corporation,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
31,JOURNAL_VOUCHER,balaji enterprises,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
32,JOURNAL_VOUCHER,cash trading co,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
33,JOURNAL_VOUCHER,entrepreneurs organization,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
34,JOURNAL_VOUCHER,kohinoor transport,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
35,JOURNAL_VOUCHER,mobitech creation pvt ltd,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
36,JOURNAL_VOUCHER,crockery centre,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
37,JOURNAL_VOUCHER,sharma & associate firetech pvt ltd,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
38,JOURNAL_VOUCHER,shreeji publicity,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
39,JOURNAL_VOUCHER,silverline enterprises,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
40,JOURNAL_VOUCHER,sonu industries,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
41,JOURNAL_VOUCHER,the creative corner,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
42,JOURNAL_VOUCHER,valley textile,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
43,JOURNAL_VOUCHER,vasundhara infocomm,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
44,JOURNAL_VOUCHER,vision technology,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
45,JOURNAL_VOUCHER,krsna glassic (pd),"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
46,JOURNAL_VOUCHER,tirupati laminates,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
47,JOURNAL_VOUCHER,007 computech,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
48,JOURNAL_VOUCHER,m-tech software,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
49,JOURNAL_VOUCHER,wow crest indore ihcl selection,"1. The user has the ability to accept or delete entries if the ledger name is not present in Tally. In such cases, the entry will be moved to Import > Exception.",NO,"1. It is preferable to use digital documents.
2. In case of failure or skipped requests, do not resend the request via the software, as repeated requests may block your request. If this happens, please contact the support team."
