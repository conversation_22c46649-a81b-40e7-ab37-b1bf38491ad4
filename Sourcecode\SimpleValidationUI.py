"""
Simple Validation UI for Accountants
Clean, focused interface showing only essential information
"""

import sys
import os
import json
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFrame, QTextEdit, QMessageBox, QDialog,
    QLineEdit, QFileDialog, QFormLayout, QSpinBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QIcon
from datetime import datetime
from PreRequestValidation import CRequestValidator
from CustomHelper import CGeneralHelper, CResoucedataHelper
from CustomLogger import CLogger
import xml.etree.ElementTree as ET
from PyQt5 import QtCore
QtCore.QCoreApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
QtCore.QCoreApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)




class ValidationWorkerThread(QThread):
    """Background validation worker"""

    validation_completed = pyqtSignal(dict)  # results

    def __init__(self, parent=None):
        super().__init__(parent)
        self.validator = None

    def run(self):
        """Run validation checks"""
        try:
            # Create validator and run validation
            self.validator = CRequestValidator(bRaiseExceptionOnSingleError=False)
            self.validator.MSRunAllValidation()

            # Check internet connection
            internet_connected = CRequestValidator.check_internet_connection()

            # Get version info
            version = "Unknown"
            try:
                config_path = os.path.join(CGeneralHelper.MSGetResourceDirectory(), "AccuveloctyConfig.json")
                if os.path.exists(config_path):
                    with open(config_path, 'r') as f:
                        config = json.load(f)
                        version = config.get("Exe_version", "Unknown")
            except:
                pass

            # Emit results with connection status
            results = {
                "warnings": self.validator.lsWarningMessages,
                "info": self.validator.lsInfoMessages,
                "critical_errors": self.validator.lsCriticalErrorMessages,
                "allow_processing": self.validator.MAllowToProcess(),
                "validation_connected": self.validator.MValidateFilesNDirError(),
                "version": version,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "tally_connected": self.validator.bTallyConnected,
                "server_connected": self.validator.bAccuVelocityServerRunning,
                "internet_connected": internet_connected
            }

            self.validation_completed.emit(results)

        except Exception as e:
            CLogger.MCWriteLog("error", f"Validation thread error: {str(e)}")
            self.validation_completed.emit({
                "warnings": [f"Validation error: {str(e)}"],
                "info": [],
                "critical_errors": [],
                "allow_processing": False,
                "version": "Unknown",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "tally_connected": False,
                "server_connected": False,
                "internet_connected": False
            })


class SettingsDialog(QDialog):
    """Simple settings dialog for Tally configuration"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Tally Configuration")
        self.setModal(True)
        self.setFixedSize(500, 300)
        self.setup_ui()
        self.load_current_settings()

    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Title
        title = QLabel("Configure Tally Settings")
        title.setFont(QFont("Segoe UI", 14, QFont.Bold))
        title.setStyleSheet("color: #2196F3; margin-bottom: 10px;")
        layout.addWidget(title)

        # Form layout
        form_layout = QFormLayout()
        form_layout.setSpacing(10)

        # Tally Port
        self.port_spinbox = QSpinBox()
        self.port_spinbox.setRange(1000, 65535)
        self.port_spinbox.setValue(9000)
        self.port_spinbox.setStyleSheet("padding: 5px; font-size: 12px;")
        form_layout.addRow("Tally Port:", self.port_spinbox)

        # Tally Company Data Location
        data_layout = QHBoxLayout()
        self.data_path_edit = QLineEdit()
        self.data_path_edit.setStyleSheet("padding: 5px; font-size: 12px;")
        self.data_browse_btn = QPushButton("Browse...")
        self.data_browse_btn.clicked.connect(self.browse_data_location)
        data_layout.addWidget(self.data_path_edit, 1)
        data_layout.addWidget(self.data_browse_btn)
        form_layout.addRow("Company Data Location:", data_layout)

        # Tally INI File
        ini_layout = QHBoxLayout()
        self.ini_path_edit = QLineEdit()
        self.ini_path_edit.setStyleSheet("padding: 5px; font-size: 12px;")
        self.ini_browse_btn = QPushButton("Browse...")
        self.ini_browse_btn.clicked.connect(self.browse_ini_file)
        ini_layout.addWidget(self.ini_path_edit, 1)
        ini_layout.addWidget(self.ini_browse_btn)
        form_layout.addRow("Tally Application INI File:", ini_layout)

        layout.addLayout(form_layout)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.save_btn = QPushButton("Save Settings")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.save_btn.clicked.connect(self.save_settings)

        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)

    def load_current_settings(self):
        """Load current settings"""
        try:
            user_config = CResoucedataHelper.MSGetUserConfig()

            # Load Tally INI path
            tally_ini = user_config.get("TALLY_INI", "")
            self.ini_path_edit.setText(tally_ini)

            # Load Config XML path and extract TallyComDataPath
            config_xml_path = user_config.get("CONFIG_XML", "")
            if config_xml_path and os.path.exists(config_xml_path):
                try:
                    tree = ET.parse(config_xml_path)
                    root = tree.getroot()
                    data_path_elem = root.find('.//TallyComDataPath')
                    if data_path_elem is not None and data_path_elem.text:
                        self.data_path_edit.setText(data_path_elem.text.strip())
                except:
                    pass

            # Load port from Tally.ini if available
            if tally_ini and os.path.exists(tally_ini):
                try:
                    import configparser
                    config = configparser.ConfigParser(strict=False, allow_no_value=True, interpolation=None)
                    config.read(tally_ini)
                    port = config.getint('TALLY', 'ServerPort', fallback=9000)
                    self.port_spinbox.setValue(port)
                except:
                    pass

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to load settings: {str(e)}")

    def browse_data_location(self):
        """Browse for Tally company data location"""
        folder = QFileDialog.getExistingDirectory(self, "Select Tally Company Data Location")
        if folder:
            self.data_path_edit.setText(folder)

    def browse_ini_file(self):
        """Browse for Tally INI file"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Tally INI File", "", "INI Files (*.ini);;All Files (*)")
        if file_path:
            self.ini_path_edit.setText(file_path)

    def save_settings(self):
        """Save settings to configuration files"""
        try:
            # Update UserConfig
            resource_dir = CGeneralHelper.MSGetResourceDirectory()
            user_config_path = os.path.join(resource_dir, "UserConfig.json")

            if os.path.exists(user_config_path):
                with open(user_config_path, 'r') as f:
                    user_config = json.load(f)

                # Update TALLY_INI path
                user_config["TALLY_INI"] = self.ini_path_edit.text()

                # Save UserConfig
                with open(user_config_path, 'w') as f:
                    json.dump(user_config, f, indent=4)

                # Update Config XML with TallyComDataPath
                config_xml_path = user_config.get("CONFIG_XML", "")
                if config_xml_path and os.path.exists(config_xml_path):
                    try:
                        tree = ET.parse(config_xml_path)
                        root = tree.getroot()

                        # Find or create TallyComDataPath element
                        data_path_elem = root.find('.//TallyComDataPath')
                        if data_path_elem is None:
                            # Create new element under CONFIGVARS
                            config_vars = root.find('CONFIGVARS')
                            if config_vars is not None:
                                data_path_elem = ET.SubElement(config_vars, 'TallyComDataPath')

                        if data_path_elem is not None:
                            data_path_elem.text = self.data_path_edit.text()
                            tree.write(config_xml_path)

                    except Exception as e:
                        CLogger.MCWriteLog("error", f"Failed to update Config XML: {str(e)}")

                QMessageBox.information(self, "Settings Saved",
                    "Settings have been saved successfully!\n\nPlease restart TallyPrime for changes to take effect.")
                self.accept()

            else:
                QMessageBox.warning(self, "Error", "UserConfig.json file not found!")

        except Exception as e:
            CLogger.MCWriteLog("error", f"Failed to save settings: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to save settings:\n{str(e)}")


class SimpleValidationWindow(QMainWindow):
    """Simple validation window for accountants"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.validation_results = {}
        self.worker_thread = None

        self.setup_ui()
        self.setup_connections()
        self.start_initial_validation()

    def setup_ui(self):
        """Setup the main UI"""
        self.setWindowTitle("AccuVelocity - System Status")
        self.setFixedSize(780, 520)

        # Set application icon
        try:
            resource_dir = CGeneralHelper.MSGetResourceDirectory()
            icon_path = os.path.join(resource_dir, "AvLogo.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except:
            pass

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 15, 20, 15)
        main_layout.setSpacing(15)

        # Connection status section (no header)
        self.create_connection_status(main_layout)

        # Issues section (expanded to cover both message and issues space)
        self.create_issues_section(main_layout)

        # Buttons
        self.create_buttons(main_layout)

        # Footer with version
        self.create_footer(main_layout)


    def create_connection_status(self, parent_layout):
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 0px solid #e0e0e0;
                border-radius: 10px;
            }
        """)

        outer = QVBoxLayout(status_frame)
        outer.setSpacing(12)                 # vertical space BETWEEN rows
        outer.setContentsMargins(10, 10, 10, 10)

        # title = QLabel("Connection Status")
        # title.setFont(QFont("Segoe UI", 13, QFont.Bold))
        # title.setStyleSheet("color:#333;")
        # outer.addWidget(title)

        def make_row(label_text):
            row = QHBoxLayout()
            row.setContentsMargins(0, 0, 0, 0)
            row.setSpacing(12)               # space BETWEEN label and pill

            left = QLabel(label_text)
            left.setFont(QFont("Segoe UI", 11, QFont.Bold))   # BOLD LABEL
            left.setStyleSheet("color:#3a3a3a;")
            # allow the text to take all remaining width, pushing pill to the right
            left.setMinimumWidth(0)

            pill = QLabel("Checking…")
            pill.setFont(QFont("Segoe UI", 10, QFont.DemiBold))
            pill.setAlignment(Qt.AlignCenter)
            pill.setFixedHeight(25)          # taller pill so rows don’t collide
            pill.setMinimumWidth(150)
            pill.setStyleSheet("""
                QLabel {
                    background-color:#FFF3CD; color:#7a5c00;
                    border:0px solid #FFE08A; border-radius:15px;
                    padding:4px 14px;         /* internal padding */
                    margin-top:2px;           /* OUTER margins to separate rows */
                    margin-bottom:2px;
                }
            """)

            row.addWidget(left, 1)           # stretch on the label side
            row.addStretch()
            row.addWidget(pill, 0, Qt.AlignRight)

            outer.addLayout(row)
            return pill

        self.tally_status_pill     = make_row("Tally")
        self.server_status_pill    = make_row("AccuVelocity Server")
        self.internet_status_pill  = make_row("Internet")
        self.validation_status_pill  = make_row("Validation Important Files & Folders")
        
        parent_layout.addWidget(status_frame)







    def create_issues_section(self, parent_layout):
        """Create issues display section with table and trigger-based background"""
        self.issues_frame = QFrame()
        self.issues_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 10px;
                padding: 12px;
            }
        """)

        layout = QVBoxLayout(self.issues_frame)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Issues title (will show count)
        self.issues_title = QLabel("Issue Detail")
        self.issues_title.setFont(QFont("Segoe UI", 13, QFont.Bold))
        self.issues_title.setStyleSheet("color: #333333; margin-bottom: 8px;")
        layout.addWidget(self.issues_title)

        # Issues text area
        self.issues_text = QTextEdit()
        self.issues_text.setReadOnly(True)
        self.issues_text.setMaximumHeight(280)
        self.issues_text.setMinimumHeight(120)
        self.issues_text.setFont(QFont("Segoe UI", 10))
        self.issues_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #d0d0d0;
                border-radius: 5px;
                padding: 10px;
                background-color: white;
                line-height: 1.4;
            }
        """)
        self.issues_text.setPlainText("Click 'Check System' to run validation...")

        layout.addWidget(self.issues_text)
        parent_layout.addWidget(self.issues_frame)


    def populate_issues_text(self, issues_list, total_count=0):
        """Populate the issues text box with validation results and SR NO prefixes"""
        if not issues_list:
            self.issues_text.setPlainText("System ready for processing. Validation completed successfully with no issues found. Click 'Check System' to run validation again.")
            self.issues_title.setText("Issue Detail (0 issues found)")
            return

        # Update title with count
        self.issues_title.setText(f"Issue Detail ({total_count} issues found)")

        # Build text with SR NO prefixes
        issues_text = []

        for i, issue in enumerate(issues_list, 1):
            # Format: SR NO. ERROR_TYPE: Title | Comments
            sr_no = f"{i}."
            error_type = issue.get('type', '')
            title = issue.get('title', '')
            comments = issue.get('comments', '')

            if comments:
                line = f"{sr_no} {error_type}: {title} | {comments}"
            else:
                line = f"{sr_no} {error_type}: {title}"

            issues_text.append(line)

        # Set the text
        self.issues_text.setPlainText("\n\n".join(issues_text))


    def create_buttons(self, parent_layout):
        """Create action buttons"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # Check System button
        self.check_btn = QPushButton("🔄 Check System")
        self.check_btn.setFont(QFont("Segoe UI", 10, QFont.Bold))
        self.check_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                min-width: 110px;
                min-height: 25px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        # Settings button
        self.settings_btn = QPushButton("⚙️ Tally Configuration")
        self.settings_btn.setFont(QFont("Segoe UI", 10, QFont.Bold))
        self.settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                min-width: 110px;
                min-height: 25px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        # Help button
        self.help_btn = QPushButton("❓ Need Help?")
        self.help_btn.setFont(QFont("Segoe UI", 10, QFont.Bold))
        self.help_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                min-width: 110px;
                min-height: 25px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

        button_layout.addStretch()
        button_layout.addWidget(self.check_btn)
        button_layout.addWidget(self.settings_btn)
        button_layout.addWidget(self.help_btn)
        button_layout.addStretch()

        parent_layout.addLayout(button_layout)

    def create_footer(self, parent_layout):
        """Create footer with version info"""
        footer_layout = QHBoxLayout()
        footer_layout.setContentsMargins(5, 5, 5, 5)

        self.version_label = QLabel("Version: Loading...")
        self.version_label.setFont(QFont("Segoe UI", 9))
        self.version_label.setStyleSheet("color: #666666;")

        footer_layout.addWidget(self.version_label)
        footer_layout.addStretch()

        # Status bar equivalent
        self.status_label = QLabel("Ready")
        self.status_label.setFont(QFont("Segoe UI", 9))
        self.status_label.setStyleSheet("color: #666666;")

        footer_layout.addWidget(self.status_label)

        parent_layout.addLayout(footer_layout)

    def setup_connections(self):
        """Setup signal connections"""
        self.check_btn.clicked.connect(self.start_validation)
        self.settings_btn.clicked.connect(self.open_settings)
        self.help_btn.clicked.connect(self.show_help)

    def start_initial_validation(self):
        """Start initial validation on startup"""
        # Set initial status
        self.update_connection_status_labels("CHECKING")

        # Start validation after a short delay
        QTimer.singleShot(1000, self.start_validation)

    def update_connection_status_labels(self, status="CHECKING",
                                    tally_connected=None, server_connected=None, internet_connected=None, validation_connected=None):
        """Update connection status pills with consistent visuals"""
        def set_pill(widget: QLabel, state: str):
            if state == "CHECKING":
                widget.setText("Checking…")
                widget.setStyleSheet("""
                    QLabel { background-color:#FFF3CD; color:#7a5c00;
                            border:0px solid #FFE08A; border-radius:13px; padding:4px 14px;margin-top:2px;  margin-bottom:2px;  }
                """)
            elif state == "OK":
                widget.setText("CONNECTED")
                widget.setStyleSheet("""
                    QLabel { background-color:#D4EDDA; color:#155724;
                            border:0px solid #C3E6CB; border-radius:13px; padding:2px 10px; }
                """)
            else:
                widget.setText("DISCONNECTED")
                widget.setStyleSheet("""
                    QLabel { background-color:#F8D7DA; color:#721C24;
                            border:1px solid #F5C6CB; border-radius:13px; padding:2px 10px; }
                """)

        if status == "CHECKING":
            set_pill(self.tally_status_pill, "CHECKING")
            set_pill(self.server_status_pill, "CHECKING")
            set_pill(self.internet_status_pill, "CHECKING")
            set_pill(self.validation_status_pill, "CHECKING")
            return

        set_pill(self.tally_status_pill,   "OK" if tally_connected else "BAD")
        set_pill(self.server_status_pill,  "OK" if server_connected else "BAD")
        set_pill(self.internet_status_pill,"OK" if internet_connected else "BAD")
        set_pill(self.validation_status_pill, "OK" if validation_connected else "BAD")
        
    def start_validation(self):
        """Start validation process"""
        if self.worker_thread and self.worker_thread.isRunning():
            return

        # Disable buttons
        self.set_buttons_enabled(False)
        self.check_btn.setText("🔄 Checking...")
        self.status_label.setText("Running system checks...")

        # Reset connection status to CHECKING
        self.update_connection_status_labels("CHECKING")

        # Clear issues text and show checking message
        self.issues_text.setPlainText("Running validation checks...")
        self.issues_title.setText("Issue Detail")

        # Start worker thread
        self.worker_thread = ValidationWorkerThread()
        self.worker_thread.validation_completed.connect(self.validation_completed)
        self.worker_thread.finished.connect(self.validation_finished)
        self.worker_thread.start()

    def set_buttons_enabled(self, enabled: bool):
        """Enable/disable buttons"""
        self.check_btn.setEnabled(enabled)
        self.settings_btn.setEnabled(enabled)
        self.help_btn.setEnabled(enabled)



    def validation_completed(self, results: dict):
        """Handle validation completion"""
        self.validation_results = results

        # Update connection status labels
        self.update_connection_status_labels(
            status="COMPLETED",
            tally_connected=results.get("tally_connected", False),
            server_connected=results.get("server_connected", False),
            internet_connected=results.get("internet_connected", False),
            validation_connected=results.get("validation_connected", False)
        )

        # Update version
        self.version_label.setText(f"Version: {results.get('version', 'Unknown')}")

        # Prepare issues for table display
        issues_list = []

        # Add critical errors
        for error in results.get("critical_errors", []):
            # Split error message into title and comments
            error_parts = error.split(" | ", 1) if " | " in error else [error, ""]
            issues_list.append({
                'type': '🚨 CRITICAL',
                'title': error_parts[0],
                'comments': error_parts[1] if len(error_parts) > 1 else ""
            })

        # Add warnings
        for warning in results.get("warnings", []):
            warning_parts = warning.split(" | ", 1) if " | " in warning else [warning, ""]
            issues_list.append({
                'type': '⚠️ WARNING',
                'title': warning_parts[0],
                'comments': warning_parts[1] if len(warning_parts) > 1 else ""
            })

        # Add info messages
        for info in results.get("info", []):
            info_parts = info.split(" | ", 1) if " | " in info else [info, ""]
            issues_list.append({
                'type': 'ℹ️ INFO',
                'title': info_parts[0],
                'comments': info_parts[1] if len(info_parts) > 1 else ""
            })

        # Calculate total count
        total_count = len(issues_list)

        # Update text with issues
        self.populate_issues_text(issues_list, total_count)

    def validation_finished(self):
        """Handle validation thread completion"""
        self.set_buttons_enabled(True)
        self.check_btn.setText("🔄 Check System")

        if self.validation_results.get("allow_processing", False):
            self.status_label.setText("✅ System ready for processing")
        else:
            self.status_label.setText("⚠️ Issues found - please review and fix")

    def open_settings(self):
        """Open settings dialog"""
        settings_dialog = SettingsDialog(self)
        if settings_dialog.exec_() == QDialog.Accepted:
            QMessageBox.information(self, "Settings Updated",
                "Settings have been updated. Click 'Check System' to validate with new settings.")

    def show_help(self):
        """Show help information"""
        help_text = """
        <h3 style="color: #4CAF50;">🔧 Common Solutions</h3>

        <p><strong>If Tally is DISCONNECTED:</strong></p>
        <ul>
        <li>Make sure TallyPrime is running on your computer</li>
        <li>In TallyPrime: Help → Settings → Connectivity → Client/Server Configuration</li>
        <li>Set <em>TallyPrime acts as</em> to <strong>Both</strong></li>
        <li>Set <em>Enable ODBC</em> to <strong>Yes</strong></li>
        <li>Set <em>Port</em> to <strong>9000</strong></li>
        <li>Restart TallyPrime</li>
        </ul>

        <p><strong>If AccuVelocity Server is DISCONNECTED:</strong></p>
        <ul>
        <li>Check your internet connection</li>
        <li>Try again after a few minutes</li>
        <li>Contact support if the problem persists</li>
        </ul>

        <p><strong>If Internet is DISCONNECTED:</strong></p>
        <ul>
        <li>Check your network connection</li>
        <li>Restart your router/modem</li>
        <li>Contact your internet service provider</li>
        </ul>

        <hr>

        <h3 style="color:#D32F2F;">🚨 CRITICAL</h3>
        <p>
        A critical error occurred. Please contact the support team or call
        <strong>+91 98989 42935</strong> for immediate help.
        </p>

        <hr>

        <h3 style="color: #FF9800;">📞 Need More Help?</h3>

        <p><strong>📧 Email Support:</strong><br>
        <a href="mailto:<EMAIL>" style="color: #2196F3;"><EMAIL></a></p>

        <p><strong>📱 Phone Support:</strong><br>
        <a href="tel:+919898942935" style="color: #2196F3;">+91 98989 42935</a></p>

        <p><strong>🕒 Support Hours:</strong><br>
        Monday to Friday: 10:30 AM to 8:00 PM IST</p>
        """


        msg = QMessageBox(self)
        msg.setWindowTitle("Help & Support")
        msg.setTextFormat(Qt.RichText)
        msg.setText(help_text)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.exec_()

    def closeEvent(self, event):
        """Handle window close event"""
        if self.worker_thread and self.worker_thread.isRunning():
            self.worker_thread.terminate()
            self.worker_thread.wait(3000)
        event.accept()


def launch_system_check():
    """
    Launch the validation UI for system checking
    Returns the window instance for external control
    """
    try:
        # Get or create QApplication instance
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        # Set application properties
        app.setApplicationName("AccuVelocity System Status")
        app.setApplicationVersion("2.1")
        app.setOrganizationName("AccuVelocity")

        # Setup logging
        try:
            CLogger.MCSetupLogging()
        except:
            pass

        # Create and show window
        window = SimpleValidationWindow()
        window.show()

        # Center window on screen
        screen = app.primaryScreen().availableGeometry()
        window.move(
            (screen.width() - window.width()) // 2,
            (screen.height() - window.height()) // 2
        )

        return window

    except Exception as e:
        CLogger.MCWriteLog("error", f"Failed to launch validation UI: {str(e)}")
        return None


def main():
    """Main function for standalone execution"""
    window = launch_system_check()

    if window:
        app = QApplication.instance()
        sys.exit(app.exec_())
    else:
        sys.exit(1)


# ------------------------- DEBUGGING or EXECUTING SimpleValidationUI file --------------------------

# def main():
#     """Main function"""
#     app = QApplication(sys.argv)

#     # Set application properties
#     app.setApplicationName("AccuVelocity System Status")
#     app.setApplicationVersion("2.1")
#     app.setOrganizationName("AccuVelocity")

#     # Setup logging
#     try:
#         CLogger.MCSetupLogging()
#     except:
#         pass

#     # Create and show window
#     window = SimpleValidationWindow()
#     window.show()

#     # Center window on screen
#     screen = app.primaryScreen().availableGeometry()
#     window.move(
#         (screen.width() - window.width()) // 2,
#         (screen.height() - window.height()) // 2
#     )

#     sys.exit(app.exec_())


if __name__ == "__main__":
    main()